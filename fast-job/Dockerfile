FROM hub.shuyun.com/base/java:openjdk-11.0.20

WORKDIR /fast-job
COPY target/fast-job.jar /fast-job/fast-job.jar
COPY src/main/resources/application.properties /fast-job/application.properties
COPY src/main/resources/application-dev.yml /fast-job/application-dev.yml
COPY src/main/resources/application-spectrum.yml /fast-job/application-spectrum.yml

ENV spectrum.disableAutoUploadProperties=true

EXPOSE 8080

CMD ["java", "-Dmicronaut.environments=spectrum", "-XX:HeapDumpPath=/var/log/fast-job/heap.hprof", "-XX:ParallelGCThreads=4", "-XX:-CICompilerCountPerCPU", "-XX:CICompilerCount=4", "-XX:NewRatio=1", "-Xms1228m",  "-jar", "fast-job.jar"]
