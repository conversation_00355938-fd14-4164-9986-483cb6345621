package com.shuyun.fast.job.resource.v1_0_0;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.job.core.BaseJob;
import com.shuyun.fast.job.core.JobRequest;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.inject.qualifiers.Qualifiers;
import io.micronaut.runtime.server.EmbeddedServer;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;

@Tag(name = "手动触发场景")
@ExecuteOn("blocking")
@Controller("/v1/0.0/api/job")
@Introspected
@Slf4j
public class JobResource {

    private final ObjectMapper objectMapper;
    private final EmbeddedServer embeddedServer;
    public JobResource(EmbeddedServer embeddedServer) {
        this.embeddedServer = embeddedServer;
        objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.registerModule(new JavaTimeModule());
    }

    @Operation(summary = "任务执行")
    @Post("/execute")
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> execute(@Valid @Body JobRequest request) throws Exception{
        log.info("job request: {}", objectMapper.writeValueAsString(request));
        BaseJob job = embeddedServer.getApplicationContext().getBean(BaseJob.class, Qualifiers.byName(request.getJobName()));
        job.execute(request);
        return ApiResult.success("success");
    }

}
