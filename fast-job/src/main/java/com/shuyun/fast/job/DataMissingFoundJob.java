package com.shuyun.fast.job;

import com.shuyun.fast.factory.ThreadPoolExecutorFactory;
import com.shuyun.fast.job.core.BaseJob;
import com.shuyun.fast.job.core.JobException;
import com.shuyun.fast.job.core.JobRequest;
import io.micronaut.scheduling.annotation.Scheduled;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

@Singleton
@Slf4j
@Named("dataMissingFoundJob")
public class DataMissingFoundJob extends BaseJob {


    @Inject
    private JdbcTemplate jdbcTemplate;

    private ThreadPoolExecutor poolExecutor = null;


    public void afterInit(){
        poolExecutor = ThreadPoolExecutorFactory.get(32, 64, 32, this.getClass().getName());
        poolExecutor.prestartAllCoreThreads();
    }

    @Scheduled(cron = "${data.missing.found.cron}" ,initialDelay = "10s")
    @Override
    public void start() throws JobException {
        JobRequest request = new JobRequest();
        super.execute(request);
    }

    @Override
    public void beforeJob(JobRequest request) throws JobException{
        super.beforeJob(request);
    }

    @Override
    public void doJob(JobRequest request) throws JobException {
//        String sql = "insert ignore into j9ijer1itc_fast_service.his_yingjia_member_missing select a.yingjia dwId from (select yingjia from j9ijer1itc_fast_service.his_yingjia_data where id >= @start and id < @end) a left join j9ijer1itc_datamodel.d_m_m_p_Member_185 b on a.yingjia = b.dataWinnerId where b.dataWinnerId is null";

        Map<String, Object> extention = request.getExtension();
        String sql = (String) extention.get("sql");
        Integer startPage = (Integer)extention.get("startPage");
        Integer endPage = (Integer)extention.get("endPage");
        Integer pageSize = (Integer)extention.get("pageSize");
        Boolean test = (Boolean) extention.get("test");
        for(; startPage<= endPage; startPage++){
            Integer startId = startPage * pageSize;
            Integer endId = (startPage + 1) * pageSize;

            try {
                poolExecutor.getQueue().put(()->{
                    String sqlE = sql.replace("@start", String.valueOf(startId));
                    String sqlF = sqlE.replace("@end", String.valueOf(endId));
                    log.info("sql:{}", sqlF);
                    if(test){

                    }else{
                        jdbcTemplate.execute(sqlF);
                    }

                });
            } catch (Exception e){
                log.error("error...", e);
            }

        }

        log.info("任务处理结束");

    }
}
