package com.shuyun.fast.job.core;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class JobException extends RuntimeException{

    private Integer code;

    public JobException(){
        super();
    }

    public JobException(String message){
        super(message);
    }

    public JobException(Integer code, String message){
        super(message);
        this.code = code;
    }
}
