package com.shuyun.fast.job;

import com.shuyun.fast.job.core.BaseJob;
import com.shuyun.fast.job.core.JobException;
import com.shuyun.fast.job.core.JobRequest;
import io.micronaut.scheduling.annotation.Scheduled;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Singleton
@Slf4j
@Named("helloJob")
public class HelloJob extends BaseJob {


    @Scheduled(cron = "0/1 * * * * ? ")
    @Override
    public void start() throws JobException {
        JobRequest request = new JobRequest();
        super.execute(request);
    }

    @Override
    public void doJob(JobRequest request) throws JobException {
        log.info("start hello job");
        try {
            Thread.sleep(5000L);
        } catch (Exception e){

        }
        log.info("end hello job");
    }

    @Override
    public Integer parallelism() {
        return 2;
    }

}
