package com.shuyun.fast.job.core;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.PostConstruct;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public abstract class BaseJob {

    final private AtomicInteger parallelism = new AtomicInteger();
    private String name;

    @Inject
    @Named("job")
    ExecutorService executorService;

    @PostConstruct
    public void init() {
        parallelism.set(parallelism());
        name = this.getClass().getName();
        afterInit();
    }

    /**
     * do something when bean initialization,can be override by sub class
     */
    public void afterInit(){

    }

    /**
     * entrance of the job which run mode is auto, must be override by sub class and annotated with {@link io.micronaut.scheduling.annotation.Scheduled}
     * @throws JobException
     */
    public void start() throws JobException{

    }

    /**
     * entrance of the job which run mode is manual, usually invoked by api invoking.
     * @param request
     * @throws JobException
     */
    public void execute(JobRequest request) throws JobException{
        executorService.execute(()->{
            try {
                beforeJob(request);
                doJob(request);
            } catch (Exception e){
                log.error("job exception", e);
            } finally{
                afterJob(request);
            }
        });

    }

    /**
     * do something before job,if override by sub class the first line must be super.beforeJob(reqyest)
     * @param request
     * @throws JobException
     */
    public void beforeJob(JobRequest request) throws JobException{
        if(parallelism.decrementAndGet() < 0){
            throw new JobException("parallelism exceeded, ignore this executing of job:" + name);
        }
    }

    /**
     * do job
     * @param request
     * @throws JobException
     */
    public abstract void doJob(JobRequest request) throws JobException;

    /**
     * parallelism of the job, can be override by sub class
     * @return
     */
    public Integer parallelism() {
        return 1;
    }

    /**
     * do something after job,if override by sub class the first line must be super.afterJob(reqyest)
     * @param request
     * @throws JobException
     */
    public void afterJob(JobRequest request) throws JobException{
        parallelism.incrementAndGet();
    }

}
