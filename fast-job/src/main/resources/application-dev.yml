datasources:
  default:
    dialect: MYSQL
    connectionInitSql: set names utf8mb4;
    jdbcUrl: *******************************************************************************************************************************************************
    username: root
    password: "root"
    driverClassName: com.mysql.cj.jdbc.Driver
    maximum-pool-size: 50
    minIdle: 15
    connectionTimeout: 60000
    keepaliveTime: 60000

flyway:
  datasources:
    default:
      enabled: true
      locations:
        - classpath:/db/migration

#swagger-ui:
#  enabled: true
kafka:
  bootstrap:
    servers: *************:9092

fast:
  redis:
    enable: true
    ssl: false
    address: redis://*************:6379
    password: fzucxl
    poolSize: 64
    database: 0
    nettyThread: 64

passport:
  enable: true
  clientId: fast-job
  clientName: fast-job

member:
  ebrand:
    #    enable: {fast.member.ebrand.enable: false}
    enable: false

benefit:
  enable: true
#  enable: {fast.benefit.ebrand.enable}
  consumer:
  selector:
    refresh:
      rate: 20s
      delay: 2s

snowflake:
  enable: true

