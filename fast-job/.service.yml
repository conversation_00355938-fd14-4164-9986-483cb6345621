kind: Service
specVersion: v4
metadata:
  name: fast-job
  apiVersion: v1
  accessPoint:
    container: fast/fast-job
  middleware:
    mysql: {}

containers:
  - name: fast/fast-job
    imagePullPolicy: PullIfNotPresent
    ports:
      - name: fast-job
        containerPort: 8080
        targetPort: 0
        protocol: tcp
profiles:
  - mem: 4096
    cpu: 0.5
    name: default
    replicas: 1
    envs:
      profileActive: dev
    containers:
      - mem: 4096
        cpu: 0.5
        name: fast/fast-job