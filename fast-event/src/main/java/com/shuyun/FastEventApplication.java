package com.shuyun;

import com.shuyun.air.kylin.micronaut.support.conf.Configuration;
import com.shuyun.fast.resource.v1_0_0.BenefitCacheResource;
import com.shuyun.fast.resource.v1_0_0.BizCacheResource;
import com.shuyun.fast.resource.v1_0_0.KafkaResource;
import com.shuyun.fast.resource.v1_0_0.RedisResource;
import io.micronaut.openapi.annotation.OpenAPIInclude;
import io.micronaut.runtime.Micronaut;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import lombok.extern.slf4j.Slf4j;

@OpenAPIDefinition(
        info = @Info(
                title = "fast-event apis",
                version = "1.0",
                description = "©2023 Copyright. Powered By shuyun.",
                license = @License(name = "Apache 2.0"),
                contact = @Contact(name = "fast-event apis", email = "<EMAIL>")
        )
)
@OpenAPIInclude(classes = {BizCacheResource.class,
        BenefitCacheResource.class,
        KafkaResource.class,
        RedisResource.class})
@Slf4j
public class FastEventApplication {

    public static void main(String[] args) {
        Configuration.get();
        Micronaut.build(args)
                .environments(args)
                .packages("com.shuyun.air.kylin.micronaut")
                .eagerInitSingletons(true)
                .mainClass(FastEventApplication.class)
                .start();
        log.info("服务启动成功");
    }
}