package com.shuyun.fast.event.service;


import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.client.v1_0_0.OpenApiFeignClient;
import com.shuyun.fast.event.config.AzureKafkaConfigEnum;
import com.shuyun.fast.event.config.AzureKafkaPropertiesLoader;
import com.shuyun.fast.event.entity.OcpMemberBindingDto;
import com.shuyun.fast.event.entity.OcpMemberDto;
import com.shuyun.fast.event.enums.BehaviorEnums;
import com.shuyun.fast.event.kafka.KafkaThreadLocal;
import com.shuyun.fast.kafka.KafkaSender;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.param.member.MemberBingDto;
import io.micronaut.core.annotation.NonNull;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Singleton
@Slf4j
public class CosmosService {

    private final KafkaSender kafkaSender;

    private final OpenApiFeignClient openApiFeignClient;

    private final ThreadLocal<KafkaProducer<String, String>> memberProducerThreadLocal = new ThreadLocal<>();

    private final ThreadLocal<KafkaProducer<String, String>> activityProducerThreadLocal = new ThreadLocal<>();

    private final ThreadLocal<KafkaProducer<String, String>> memberDeleteProducerThreadLocal = new ThreadLocal<>();

    private final ThreadLocal<KafkaProducer<String, String>> wechatoaeventProducerThreadLocal = new ThreadLocal<>();

    private final ThreadLocal<KafkaProducer<String, String>> wechatsubscriptionProducerThreadLocal = new ThreadLocal<>();

    private final ThreadLocal<KafkaProducer<String, String>> masterDataProducerThreadLocal = new ThreadLocal<>();

    public CosmosService(OpenApiFeignClient openApiFeignClient, KafkaSender kafkaSender) {
        this.openApiFeignClient = openApiFeignClient;
        this.kafkaSender = kafkaSender;
    }

    /**
     * cosmos会员注册/更新,会员等级/积分更新
     *
     * @param ocpMemberBindingDto
     */
    public void queryCosmosMember(OcpMemberBindingDto ocpMemberBindingDto) {
        //指定要查询的字段(扩展)
        List<String> optionalFields = Arrays.asList("memberSource", "memberSourceDetail");

        //查询会员渠道信息
        List<MemberBingDto> memberBingDtos = openApiFeignClient.queryListChannels(ocpMemberBindingDto.getMember().get("crm_member_id").toString(), "KO", optionalFields);
        log.info("cdp查询会员渠道信息:{}", JsonUtil.outPutSerialize(memberBingDtos));
        OcpMemberDto memberDto = new OcpMemberDto();
        memberDto.setIs_ocp_member(ocpMemberBindingDto.getIs_ocp_member());
        memberDto.setCreated_at(ocpMemberBindingDto.getCreated_at());
        memberDto.setUpdated_at(ocpMemberBindingDto.getUpdated_at());

        if (memberBingDtos.stream().anyMatch(m -> "SWIRE_MP".equals(m.getChannelType()))) {
            memberDto.setConsumer_source("MTYL");
            memberDto.setOrganization_name("Swire");
            memberDto.setOrganization_code("SCCL");
        }
        if (memberBingDtos.stream().anyMatch(m -> "CBL_MP".equals(m.getChannelType()))) {
            memberDto.setConsumer_source("YXH");
            memberDto.setOrganization_name("COFCO");
            memberDto.setOrganization_code("CBL");
        }
        if (memberBingDtos.stream().anyMatch(m -> "KO_Ali".equals(m.getChannelType()))) {
            memberDto.setConsumer_source("Ali MP");
            memberDto.setOrganization_name(ocpMemberBindingDto.getOrganization_name());
            memberDto.setOrganization_code(ocpMemberBindingDto.getOrganization_code());
        }
        if (memberBingDtos.stream().anyMatch(m -> "KO_MP".equals(m.getChannelType()))) {
            memberDto.setConsumer_source(ocpMemberBindingDto.getConsumer_source());
            memberDto.setOrganization_name(ocpMemberBindingDto.getOrganization_name());
            memberDto.setOrganization_code(ocpMemberBindingDto.getOrganization_code());
        }

        HashMap<String, Object> campaignInfo = new HashMap<>();
        campaignInfo.put("info_authorized", ocpMemberBindingDto.getCampaign_info().get("info_authorized"));
        campaignInfo.put("campaign_id", ocpMemberBindingDto.getCampaign_info().get("campaign_id"));
        campaignInfo.put("lbs_authorized", ocpMemberBindingDto.getCampaign_info().get("lbs_authorized"));
        memberDto.setCampaign_info(campaignInfo);
        ArrayList<Map> arrayList = new ArrayList<>();

        ArrayList<Object> platformBind = new ArrayList<>();
        for (MemberBingDto bingDto : memberBingDtos) {
            HashMap<String, Object> bind = new HashMap<>();
            bind.put("channel", bingDto.getChannelType());
            bind.put("timestamp", getStartTimeDate(bingDto.getCreateTime()));
            bind.put("source", bingDto.getOptionalFieldData().get("memberSource"));
            if (StringUtils.isNotBlank(bingDto.getOptionalFieldData().get("memberSourceDetail"))) {
                bind.put("source_detail", bingDto.getOptionalFieldData().get("memberSourceDetail"));
            }
            bind.put("platform", "wechat");
            HashMap<String, String> map = new HashMap<>();
            map.put("app_id", bingDto.getAppId());
            if ("SWIRE_MP".equals(bingDto.getChannelType())) {
                map.put("app_name", "每天有乐");
                //添加openId
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "wechat_open_id");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "wechat");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_name", "每天有乐");
                    hashMap.put("app_id", bingDto.getAppId());
                    hashMap.put("app_group_name", "太古可口可乐");
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
                //添加unionId
                if (StringUtils.isNotBlank(bingDto.getUnionId())) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_group_name", "太古可口可乐");
                    HashMap<String, Object> tmallMap = new HashMap<>();
                    tmallMap.put("platform", "wechat");
                    tmallMap.put("type", "wechat_union_id");
                    tmallMap.put("value", bingDto.getUnionId());
                    tmallMap.put("attribute", hashMap);
                    arrayList.add(tmallMap);
                }
            }
            if ("CBL_MP".equals(bingDto.getChannelType())) {
                map.put("app_name", "悦喜荟");
                //添加openId
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "wechat_open_id");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "wechat");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_name", "悦喜荟");
                    hashMap.put("app_id", bingDto.getAppId());
                    hashMap.put("app_group_name", "中粮可口可乐");
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
                //添加unionId
                if (StringUtils.isNotBlank(bingDto.getUnionId())) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_group_name", "中粮可口可乐");
                    HashMap<String, Object> tmallMap = new HashMap<>();
                    tmallMap.put("platform", "wechat");
                    tmallMap.put("type", "wechat_union_id");
                    tmallMap.put("value", bingDto.getUnionId());
                    tmallMap.put("attribute", hashMap);
                    arrayList.add(tmallMap);
                }
            }
            if ("KO_Ali".equals(bingDto.getChannelType())) {
                map.put("app_name", "支付宝可口可乐吧");
                bind.put("platform", "alibaba");
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "alipay");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "alibaba");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_name", "支付宝可口可乐吧");
                    hashMap.put("app_id", bingDto.getAppId());
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
            }
            if ("KO_MP".equals(bingDto.getChannelType())) {
                map.put("app_name", "可口可乐吧");
                //添加openId
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "wechat_open_id");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "wechat");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("is_koplus_openid", ocpMemberBindingDto.getAttribute().get("is_koplus_openid"));
                    hashMap.put("app_group_name", "可口可乐");
                    hashMap.put("app_id",  bingDto.getAppId());
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
                //添加unionId
                if (StringUtils.isNotBlank(bingDto.getUnionId())) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_group_name", "可口可乐");
                    HashMap<String, Object> tmallMap = new HashMap<>();
                    tmallMap.put("platform", "wechat");
                    tmallMap.put("type", "wechat_union_id");
                    tmallMap.put("value", bingDto.getUnionId());
                    tmallMap.put("attribute", hashMap);
                    arrayList.add(tmallMap);
                }
            }
            if ("MT_MP".equals(bingDto.getChannelType())) {
                map.put("app_name", "美团");
                //添加openId
                if (StringUtils.isNotBlank(bingDto.getOpenId())) {
                    HashMap<String, Object> wechatMap = new HashMap<>();
                    wechatMap.put("type", "wechat_open_id");
                    wechatMap.put("value", bingDto.getOpenId());
                    wechatMap.put("platform", "wechat");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("is_koplus_openid", ocpMemberBindingDto.getAttribute().get("is_koplus_openid"));
                    hashMap.put("app_group_name", "可口可乐");
                    hashMap.put("app_id",  bingDto.getAppId());
                    wechatMap.put("attribute", hashMap);
                    arrayList.add(wechatMap);
                }
                //添加unionId
                if (StringUtils.isNotBlank(bingDto.getUnionId())) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("app_group_name", "可口可乐");
                    HashMap<String, Object> tmallMap = new HashMap<>();
                    tmallMap.put("platform", "wechat");
                    tmallMap.put("type", "wechat_union_id");
                    tmallMap.put("value", bingDto.getUnionId());
                    tmallMap.put("attribute", hashMap);
                    arrayList.add(tmallMap);
                }
            }
            bind.put("attribute", map);
            platformBind.add(bind);
        }

        //添加memberId
        HashMap<String, Object> memberCrm = new HashMap<>();
        memberCrm.put("platform", ocpMemberBindingDto.getCrm_member().get("platform"));
        memberCrm.put("type", ocpMemberBindingDto.getCrm_member().get("type"));
        memberCrm.put("value", ocpMemberBindingDto.getCrm_member().get("value"));
        //memberCrm.put("attribute", Collections.EMPTY_MAP);
        arrayList.add(memberCrm);

        //添加手机号
        HashMap<String, Object> memberMobile = new HashMap<>();
        memberMobile.put("platform", "phone");
        memberMobile.put("type", "phone_number");
        memberMobile.put("value", ocpMemberBindingDto.getMember().get("mobile"));
        memberMobile.put("attribute", Collections.EMPTY_MAP);
        arrayList.add(memberMobile);
        memberDto.setPlatform_info(arrayList);
        memberDto.setDemographic(ocpMemberBindingDto.getDemographic());

        HashMap<String, Object> equity = new HashMap<>();
        if (null != ocpMemberBindingDto.getMember().get("first_bottle_time")) {
            equity.put("first_bottle_time", copStartTimeDate(JsonUtil.outPutSerialize(ocpMemberBindingDto.getMember().get("first_bottle_time"))));
            equity.put("available_bottle", ocpMemberBindingDto.getMember().get("available_bottle"));
            equity.put("total_bottle", ocpMemberBindingDto.getMember().get("total_bottle"));
            equity.put("used_bottle", ocpMemberBindingDto.getMember().get("used_bottle"));
            equity.put("expired_bottle", ocpMemberBindingDto.getMember().get("expired_bottle"));
        }
        HashMap<String, Object> member = new HashMap<>();
        member.put("member_level", ocpMemberBindingDto.getMember().get("member_level"));
        member.put("register_source_detail", ocpMemberBindingDto.getMember().get("register_source_detail"));
        if (null != ocpMemberBindingDto.getMember().get("logoff_time")) {
            member.put("logoff_time", copStartTimeDate(JsonUtil.outPutSerialize(ocpMemberBindingDto.getMember().get("logoff_time"))));
        }
        if (null != ocpMemberBindingDto.getMember().get("register_time")) {
            member.put("register_time", copStartTimeDate(JsonUtil.outPutSerialize(ocpMemberBindingDto.getMember().get("register_time"))));
        }
        member.put("is_invite_register", ocpMemberBindingDto.getMember().get("is_invite_register"));
        member.put("is_logoff", ocpMemberBindingDto.getMember().get("is_logoff"));
        member.put("experience", ocpMemberBindingDto.getMember().get("experience"));
        member.put("member_type", ocpMemberBindingDto.getMember().get("member_type"));
        member.put("register_channel", ocpMemberBindingDto.getMember().get("register_channel"));
        member.put("crm_member_id", ocpMemberBindingDto.getMember().get("crm_member_id"));
        member.put("register_address", ocpMemberBindingDto.getMember().get("register_address"));
        member.put("register_source", ocpMemberBindingDto.getMember().get("register_source"));
        if (null !=  ocpMemberBindingDto.getMember().get("external_membership")) {
            List externalMembership = (List) ocpMemberBindingDto.getMember().get("external_membership");
            member.put("external_membership", externalMembership);
        }
        if (null != equity) {
            member.put("equity", equity);
        }
        member.put("platform_bind", platformBind);
        memberDto.setMember(member);
        memberDto.setData_source(ocpMemberBindingDto.getDataSource());
        log.info(ocpMemberBindingDto.getTopic()+"_同步cosmos请求入参:{}", JsonUtil.outPutSerialize(memberDto));

        ProducerRecord<String, String> record = new ProducerRecord<>(
                ocpMemberBindingDto.getTopic(),
                UUID.randomUUID().toString(),
                JsonUtil.outPutSerialize(memberDto)
        );

        KafkaThreadLocal.getProducer(memberProducerThreadLocal, new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_MEMBER.getCode()))
            .send(record, (metadata, exception) -> {
            if (exception == null) {
                // 发送成功处理
                dataApiSyncCosmoslog(
                        ocpMemberBindingDto.getId(),
                        ocpMemberBindingDto.getFqn(),
                        ocpMemberBindingDto.getMember().get("crm_member_id").toString(),
                        null,
                        BehaviorEnums.SYNC_SUCCESS.getCode(),
                        BehaviorEnums.SYNC_SUCCESS.getName(),
                        ocpMemberBindingDto.getType()
                );
                log.info("发送成功 id:{}, onlyId:{}",
                        ocpMemberBindingDto.getId(),
                        ocpMemberBindingDto.getMember().get("crm_member_id").toString()
                );
            } else {
                // 发送失败处理
                dataApiSyncCosmoslog(
                        ocpMemberBindingDto.getId(),
                        ocpMemberBindingDto.getFqn(),
                        ocpMemberBindingDto.getMember().get("crm_member_id").toString(),
                        null,
                        BehaviorEnums.SYNC_ERROR.getCode(),
                        BehaviorEnums.SYNC_ERROR.getName(),
                        ocpMemberBindingDto.getType()
                );
                log.warn("发送失败 id:{}, onlyId:{}, msg:{}",
                        ocpMemberBindingDto.getId(),
                        ocpMemberBindingDto.getMember().get("crm_member_id").toString(),
                        exception.getMessage()
                );
            }
        });
    }


    /**
     * cosmos activity数据
     *
     * @param request
     */
    public void queryCosmosActivity(Map<String, Object> request) {

        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JsonUtil.outPutSerialize(request.get("requestBody")));
        ProducerRecord<String, String> record = new ProducerRecord<>(request.get("topic").toString(),
                UUID.randomUUID().toString(), JsonUtil.outPutSerialize(request.get("requestBody")));
        KafkaThreadLocal.getProducer(activityProducerThreadLocal, new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_ACTIVITY.getCode()))
            .send(record, (metadata, exception) -> {
            if (exception == null) {
                // 发送成功处理
                dataApiSyncCosmoslog(
                        request.get("id").toString(),
                        request.get("fqn").toString(),
                        request.get("onlyId").toString(),
                        null,
                        BehaviorEnums.SYNC_SUCCESS.getCode(),
                        BehaviorEnums.SYNC_SUCCESS.getName(),
                        request.get("type").toString()
                );
                log.info("发送成功 id:{}, onlyId:{}",
                        request.get("id").toString(),
                        request.get("onlyId").toString()
                );
            } else {
                // 发送失败处理
                dataApiSyncCosmoslog(
                        request.get("id").toString(),
                        request.get("fqn").toString(),
                        request.get("onlyId").toString(),
                        request.get("requestBody").toString(),
                        BehaviorEnums.SYNC_ERROR.getCode(),
                        BehaviorEnums.SYNC_ERROR.getName(),
                        request.get("type").toString()
                );
                log.error("发送失败 id:{}, onlyId:{}, msg:",
                        request.get("id").toString(),
                        request.get("onlyId").toString(),
                        exception
                );
            }
        });
    }

    /**
     * 小程序订阅数据
     * @param request
     */
    public void queryCosmosWechatsubscription(Map<String, Object> request) {

        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JsonUtil.outPutSerialize(request.get("requestBody")));

        ProducerRecord<String, String> record = new ProducerRecord<>(request.get("topic").toString(),
                UUID.randomUUID().toString(), JsonUtil.outPutSerialize(request.get("requestBody")));
        KafkaThreadLocal.getProducer(wechatsubscriptionProducerThreadLocal, new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_WECHATSUBSCRIPTION.getCode()))
            .send(record, (metadata, exception) -> {
            if (exception == null) {
                // 发送成功处理
                dataApiSyncCosmoslog(
                        request.get("id").toString(),
                        request.get("fqn").toString(),
                        request.get("onlyId").toString(),
                        null,
                        BehaviorEnums.SYNC_SUCCESS.getCode(),
                        BehaviorEnums.SYNC_SUCCESS.getName(),
                        request.get("type").toString()
                );
                log.info("发送成功 id:{}, onlyId:{}",
                        request.get("id").toString(),
                        request.get("onlyId").toString()
                );
            } else {
                // 发送失败处理
                dataApiSyncCosmoslog(
                        request.get("id").toString(),
                        request.get("fqn").toString(),
                        request.get("onlyId").toString(),
                        request.get("requestBody").toString(),
                        BehaviorEnums.SYNC_ERROR.getCode(),
                        BehaviorEnums.SYNC_ERROR.getName(),
                        request.get("type").toString()
                );
                log.error("发送失败 id:{}, onlyId:{}, 错误信息:{}",
                        request.get("id").toString(),
                        request.get("onlyId").toString(),
                        exception.getMessage()
                );
            }
        });

    }

    /**
     * 粉丝用户
     *
     * @param request
     */
    public void sendCosmosWechatFans(Map<String, Object> request) {

        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JsonUtil.outPutSerialize(request.get("requestBody")));
        ProducerRecord<String, String> record = new ProducerRecord<>(request.get("topic").toString(),
                UUID.randomUUID().toString(), JsonUtil.outPutSerialize(request.get("requestBody")));

        KafkaThreadLocal.getProducer(memberProducerThreadLocal, new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_MEMBER.getCode()))
            .send(record, (metadata, exception) -> {
            if (exception == null) {
                // 发送成功处理
                dataApiSyncCosmoslog(
                        request.get("id").toString(),
                        request.get("fqn").toString(),
                        request.get("onlyId").toString(),
                        null,
                        BehaviorEnums.SYNC_SUCCESS.getCode(),
                        BehaviorEnums.SYNC_SUCCESS.getName(),
                        request.get("type").toString()
                );
                log.info("发送成功 id:{}, onlyId:{}",
                        request.get("id").toString(),
                        request.get("onlyId").toString()
                );
            } else {
                // 发送失败处理
                dataApiSyncCosmoslog(
                        request.get("id").toString(),
                        request.get("fqn").toString(),
                        request.get("onlyId").toString(),
                        request.get("requestBody").toString(),
                        BehaviorEnums.SYNC_ERROR.getCode(),
                        BehaviorEnums.SYNC_ERROR.getName(),
                        request.get("type").toString()
                );
                log.error("发送失败 id:{}, onlyId:{}, 错误信息:{}",
                        request.get("id").toString(),
                        request.get("onlyId").toString(),
                        exception.getMessage()
                );
            }
        });

    }

    /**
     * 粉丝消息，粉丝行为
     *
     * @param request
     */
    public void sendCosmosWechatToaEvent(Map<String, Object> request) {

        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JsonUtil.outPutSerialize(request.get("requestBody")));

        ProducerRecord<String, String> record = new ProducerRecord<>(request.get("topic").toString(),
                UUID.randomUUID().toString(), JsonUtil.outPutSerialize(request.get("requestBody")));

        KafkaThreadLocal.getProducer(wechatoaeventProducerThreadLocal, new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_WECHATOAEVENT.getCode()))
            .send(record, (metadata, exception) -> {
            if (exception == null) {
                // 发送成功处理
                dataApiSyncCosmoslog(
                        request.get("id").toString(),
                        request.get("fqn").toString(),
                        request.get("onlyId").toString(),
                        null,
                        BehaviorEnums.SYNC_SUCCESS.getCode(),
                        BehaviorEnums.SYNC_SUCCESS.getName(),
                        request.get("type").toString()
                );
                log.info("发送成功 id:{}, onlyId:{}",
                        request.get("id").toString(),
                        request.get("onlyId").toString()
                );
            } else {
                // 发送失败处理
                dataApiSyncCosmoslog(
                        request.get("id").toString(),
                        request.get("fqn").toString(),
                        request.get("onlyId").toString(),
                        request.get("requestBody").toString(),
                        BehaviorEnums.SYNC_ERROR.getCode(),
                        BehaviorEnums.SYNC_ERROR.getName(),
                        request.get("type").toString()
                );
                log.error("发送失败 id:{}, onlyId:{}, 错误信息:{}",
                        request.get("id").toString(),
                        request.get("onlyId").toString(),
                        exception.getMessage()
                );
            }
        });

    }

    /**
     * 会员注销，用户注销
     *
     * @param request
     */
    public void sendCosmosCancel(Map<String, Object> request) {
        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JsonUtil.outPutSerialize(request.get("requestBody")));
        ProducerRecord<String, String> record = new ProducerRecord<>(request.get("topic").toString(),
                UUID.randomUUID().toString(), JsonUtil.outPutSerialize(request.get("requestBody")));

        KafkaThreadLocal.getProducer(memberDeleteProducerThreadLocal, new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_MEMBER_DELETE.getCode()))
            .send(record, (metadata, exception) -> {
            if (exception == null) {
                // 发送成功处理
                dataApiSyncCosmoslog(
                        request.get("id").toString(),
                        request.get("fqn").toString(),
                        request.get("onlyId").toString(),
                        null,
                        BehaviorEnums.SYNC_SUCCESS.getCode(),
                        BehaviorEnums.SYNC_SUCCESS.getName(),
                        request.get("type").toString()
                );
                log.info("发送成功 id:{}, onlyId:{}",
                        request.get("id").toString(),
                        request.get("onlyId").toString()
                );
            } else {
                // 发送失败处理
                dataApiSyncCosmoslog(
                        request.get("id").toString(),
                        request.get("fqn").toString(),
                        request.get("onlyId").toString(),
                        request.get("requestBody").toString(),
                        BehaviorEnums.SYNC_ERROR.getCode(),
                        BehaviorEnums.SYNC_ERROR.getName(),
                        request.get("type").toString()
                );
                log.error("发送失败 id:{}, onlyId:{}, 错误信息:{}",
                        request.get("id").toString(),
                        request.get("onlyId").toString(),
                        exception.getMessage()
                );
            }
        });
    }

    /**
     * 权益项目。小程序模版
     *
     * @param request
     */
    public void sendCosmosMaster(Map<String, Object> request) {
        log.info(request.get("topic")+"_同步cosmos请求入参:{}", JsonUtil.outPutSerialize(request));
        HashMap<Object, Object> map = new HashMap<>();
        map.put("data_type",request.get("dataType"));
        map.put("data_content",JsonUtil.outPutSerialize(request.get("dataContent")));
        map.put("data_source",request.get("dataSource"));
        log.info("请求权益主数据/小程序订阅模版数据封装:{}",JsonUtil.outPutSerialize(map));

        ProducerRecord<String, String> record =
                new ProducerRecord<>(request.get("topic").toString(), UUID.randomUUID().toString(), JsonUtil.outPutSerialize(map));

        KafkaThreadLocal.getProducer(masterDataProducerThreadLocal, new AzureKafkaPropertiesLoader(AzureKafkaConfigEnum.CDP_MASTERDATA.getCode()))
            .send(record, (metadata, exception) -> {
            if (exception == null) {
                dataApiSyncCosmoslog(
                        request.get("id").toString(),
                        request.get("fqn").toString(),
                        request.get("onlyId").toString(),
                        null,
                        BehaviorEnums.SYNC_SUCCESS.getCode(),
                        BehaviorEnums.SYNC_SUCCESS.getName(),
                        request.get("type").toString()
                );
                log.info("发送成功 - ID: {}, OnlyID: {}",
                        request.get("id"),
                        request.get("onlyId"));
            } else {
                dataApiSyncCosmoslog(
                        request.get("id").toString(),
                        request.get("fqn").toString(),
                        request.get("onlyId").toString(),
                        request.get("dataContent").toString(),
                        BehaviorEnums.SYNC_ERROR.getCode(),
                        BehaviorEnums.SYNC_ERROR.getName(),
                        request.get("type").toString()
                );
                log.error("发送失败 - ID: {}, OnlyID: {}, 错误: {}",
                        request.get("id"),
                        request.get("onlyId"),
                        exception.getMessage());
            }
        });


    }


    private void dataApiSyncCosmoslog(String id,String fqn,String onlyId,String requestBody,String code,String msg,String type){
        HashMap<String, Object> res = new HashMap<>();
        res.put("id",id);
        res.put("onlyId",onlyId);
        res.put("code",code);
        res.put("msg",msg);
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
        res.put("lastSync", LocalDateTime.now(ZoneId.of("GMT")).format(dtf));
        res.put("type",type);
        res.put("fqn",fqn);
        res.put("requestBody",requestBody);

        kafkaSender.send(ModelTags.KAFKA_COSMOS_LOG_INPUT, UUID.randomUUID().toString(), res);

    }

    public static String getStartTimeDate(String time){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeStrart = time.substring(0,19).replace("T"," ");
        String timeDate = "";
        try {
            Date dt=sdf.parse(timeStrart);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.HOUR,8);
            Date nowTime = rightNow.getTime();
            timeDate = sdf.format(nowTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timeDate;
    }
    public static String copStartTimeDate(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeDate = time.substring(1, 20).replace("T", " ");

        return timeDate;
    }
}
