package com.shuyun.fast.event.listener;

import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.event.entity.OcpMemberBindingDto;
import com.shuyun.fast.event.service.CosmosService;
import com.shuyun.fast.util.JsonUtil;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static io.micronaut.configuration.kafka.annotation.OffsetStrategy.AUTO;

@Singleton
@Slf4j
public class CosmosListener {

    private static final int THREADS = 6;

    @Inject
    private CosmosService cosmosService;

    /**
     * cosmos会员注册/更新
     *
     * @param ocpMemberBindingDto
     */
    @KafkaListener(groupId = "CRM_COSMOS_MEMBER_GROUP", offsetStrategy = AUTO, offsetReset = OffsetReset.LATEST, threads = THREADS)
    @Topic(value = ModelTags.KAFKA_COSMOS_MEMBER_INPUT)
    public void medalProgressByCampaignConsumer(OcpMemberBindingDto ocpMemberBindingDto){
        try {
            log.info("接收到cosmos会员注册/更新入参:{}", JsonUtil.outPutSerialize(ocpMemberBindingDto));
            cosmosService.queryCosmosMember(ocpMemberBindingDto);
        } catch (Exception e) {
            log.error("接收到cosmos会员注册/更新失败:{}", JsonUtil.outPutSerialize(ocpMemberBindingDto),e);
        }
    }

    /**
     * cosmos会员等级/积分更新
     *
     * @param ocpMemberBindingDto
     */
    @KafkaListener(groupId = "CRM_COSMOS_GRADE_POINT_GROUP", offsetStrategy = AUTO, offsetReset = OffsetReset.LATEST, threads = THREADS)
    @Topic(value = ModelTags.KAFKA_COSMOS_GRADE_POINT_INPUT)
    public void memberGrade(OcpMemberBindingDto ocpMemberBindingDto) {
        try {
            log.info("接收到cosmos会员等级/积分更新入参:{}", JsonUtil.outPutSerialize(ocpMemberBindingDto));
            cosmosService.queryCosmosMember(ocpMemberBindingDto);
        } catch (Exception e) {
            log.error("接收到cosmos会员等级/积分更新失败:{}", JsonUtil.outPutSerialize(ocpMemberBindingDto),e);
        }
    }

    /**
     * cosmos activity数据
     *
     * @param request
     */
    @KafkaListener(groupId = "CRM_COSMOS_ACTIVITY_GROUP", offsetStrategy = AUTO, offsetReset = OffsetReset.LATEST, threads = THREADS)
    @Topic(value = ModelTags.KAFKA_COSMOS_ACTIVITY_INPUT)
    public void memberActivity(Map<String, Object> request) {
        try {
            log.info("接收到cosmos_activity数据入参:{}", JsonUtil.outPutSerialize(request));
            cosmosService.queryCosmosActivity(request);
        } catch (Exception e) {
            log.error("接收到cosmos_activity数据失败:{}", JsonUtil.outPutSerialize(request),e);
        }
    }

    /**
     * cosmos 小程序订阅数据
     *
     * @param request
     */
    @KafkaListener(groupId = "CRM_COSMOS_WECHATSUBSCRIPTION_GROUP", offsetStrategy = AUTO, offsetReset = OffsetReset.LATEST, threads = THREADS)
    @Topic(value = ModelTags.KAFKA_COSMOS_WECHATSUBSCRIPTION_INPUT)
    public void memberWechatsubscription(Map<String, Object> request) {
        try {
            log.info("接收到cosmos_小程序订阅数据数据入参:{}", JsonUtil.outPutSerialize(request));
            cosmosService.queryCosmosWechatsubscription(request);
        } catch (Exception e) {
            log.error("接收到cosmos_小程序订阅数据数据失败:{}", JsonUtil.outPutSerialize(request),e);
        }
    }

    /**
     * cosmos 粉丝用户
     *
     * @param request
     */
    @KafkaListener(groupId = "CRM_COSMOS_WECHATFANS_GROUP", offsetStrategy = AUTO, offsetReset = OffsetReset.LATEST, threads = THREADS)
    @Topic(value = ModelTags.KAFKA_COSMOS_WECHATFANS_INPUT)
    public void cosmosWechatFans(Map<String, Object> request) {
        try {
            log.info("接收到cosmos_粉丝用户数据数据入参:{}", JsonUtil.outPutSerialize(request));
            cosmosService.sendCosmosWechatFans(request);
        } catch (Exception e) {
            log.error("接收到cosmos_粉丝用户数据数据失败:{}", JsonUtil.outPutSerialize(request),e);
        }
    }

    /**
     * cosmos 粉丝消息，粉丝行为
     *
     * @param request
     */
    @KafkaListener(groupId = "CRM_COSMOS_WECHATTOAEVENT_GROUP", offsetStrategy = AUTO, offsetReset = OffsetReset.LATEST, threads = THREADS)
    @Topic(value = ModelTags.KAFKA_COSMOS_WECHATTOAEVENT_INPUT)
    public void cosmosWechatToaEvent(Map<String, Object> request) {
        try {
            log.info("接收到cosmos_粉丝消息，粉丝行为数据数据入参:{}", JsonUtil.outPutSerialize(request));
            cosmosService.sendCosmosWechatToaEvent(request);
        } catch (Exception e) {
            log.error("接收到cosmos_粉丝消息，粉丝行为数据数据失败:{}", JsonUtil.outPutSerialize(request),e);
        }
    }

    /**
     * cosmos 会员注销，用户注销
     *
     * @param request
     */
    @KafkaListener(groupId = "CRM_COSMOS_CANCEL_GROUP", offsetStrategy = AUTO, offsetReset = OffsetReset.LATEST, threads = THREADS)
    @Topic(value = ModelTags.KAFKA_COSMOS_CANCEL_INPUT)
    public void cosmosCancel(Map<String, Object> request) {
        try {
            log.info("接收到cosmos_会员注销，用户注销数据数据入参:{}", JsonUtil.outPutSerialize(request));
            cosmosService.sendCosmosCancel(request);
        } catch (Exception e) {
            log.error("接收到cosmos_会员注销，用户注销数据数据失败:{}", JsonUtil.outPutSerialize(request),e);
        }
    }

    /**
     * cosmos 权益项目。小程序模版
     *
     * @param request
     */
    @KafkaListener(groupId = "CRM_COSMOS_MASTER_GROUP", offsetStrategy = AUTO, offsetReset = OffsetReset.LATEST, threads = THREADS)
    @Topic(value = ModelTags.KAFKA_COSMOS_MASTER_INPUT)
    public void cosmosMaster(Map<String, Object> request) {
        try {
            log.info("接收到cosmos_权益项目。小程序模版数据数据入参:{}", JsonUtil.outPutSerialize(request));
            cosmosService.sendCosmosMaster(request);
        } catch (Exception e) {
            log.error("接收到cosmos_权益项目。小程序模版数据数据失败:{}", JsonUtil.outPutSerialize(request),e);
        }
    }
}
