package com.shuyun.fast.event.service;


import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.client.v1_0_0.OpenApiFeignClient;
import com.shuyun.fast.kafka.KafkaSender;
import com.shuyun.fast.ls.result.SyncMemberResponse;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.util.ExternalApiClient;
import com.shuyun.fast.util.JsonUtil;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.shuyun.fast.client.v1_0_0.OpenApiFeignClient;
import com.shuyun.fast.event.config.AzureKafkaConfigEnum;
import org.apache.kafka.clients.producer.KafkaProducer;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Singleton
@Slf4j
public class LsService {

    private final KafkaSender kafkaSender;
    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    public LsService(KafkaSender kafkaSender) {
        this.kafkaSender = kafkaSender;

    }


    /**
     * 罗森会员接口回调
     *
     * @param request
     */
    public void lsMemberSync(Map<String, String> request) {
        try {
            String id = request.get("id");
            SyncMemberResponse syncMemberResponse = ExternalApiClient.lsSyncCall(request);
            if (syncMemberResponse.getCode().equals("00000") && syncMemberResponse.getData() != null){
                //更新联合会员表中的isFirstBinding字段
                HashMap<String, Object> map = new HashMap<>();
                SyncMemberResponse.LsMemberInfo data = syncMemberResponse.getData();
                map.put("isFirstBinding",data.isFirstBinding());
                map.put("id",id);
                map.put("lastSync",LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
                dataapiHttpSdk.update(ModelTags.DATA_FQN_UNION_MEMBER_CHANNEL, id,map,false);
            }
            dataApiSyncLslog(request.get("orgUserId"),syncMemberResponse.getCode(),syncMemberResponse.getMessage());
            log.info("罗森会员回调结果:{}", JsonUtil.outPutSerialize(syncMemberResponse));
        } catch (Exception e) {
            log.error("罗森会员回调失败:{}", e);
            dataApiSyncLslog(request.get("orgUserId"),"500","同步失败");
        }
    }


    private void dataApiSyncLslog(String id,String code,String message){
        HashMap<String, Object> res = new HashMap<>();
        res.put("id",id);
        if (StringUtils.isNotEmpty(code)){
            res.put("code",code);
        }
        if (StringUtils.isNotEmpty(message)){
            res.put("msg",message);
        }
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
        res.put("lastSync", LocalDateTime.now(ZoneId.of("GMT")).format(dtf));
        kafkaSender.send(ModelTags.KAFKA_LS_LOG_INPUT, UUID.randomUUID().toString(), res);

    }
}
