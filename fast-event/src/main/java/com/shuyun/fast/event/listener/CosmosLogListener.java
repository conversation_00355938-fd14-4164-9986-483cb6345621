package com.shuyun.fast.event.listener;

import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.event.entity.OcpMemberBindingDto;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.util.JsonUtil;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import io.micronaut.messaging.Acknowledgement;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static io.micronaut.configuration.kafka.annotation.OffsetStrategy.DISABLED;

@Slf4j
@Singleton
public class CosmosLogListener {


    /**
     * cosmos 日志消费
     *
     * @param request
     */
    @KafkaListener(groupId = "CRM_COSMOS_LOG_GROUP", offsetStrategy = DISABLED, offsetReset = OffsetReset.LATEST, threads = 3)
    @Topic(value = ModelTags.KAFKA_COSMOS_LOG_INPUT)
    public void cosmosLogConsumer(Map<String, Object> request, Acknowledgement acknowledgement){
        try {
            log.info("接收到cosmos日志入参:{}", JsonUtil.outPutSerialize(request));
            DMLResponse response = DataapiSdkUtil.upsertIsData((String) request.get("fqn"),(String) request.get("id"), request);
            if (!response.getIsSuccess()) {
                log.error("同步cosmos推送kafka记录日志失败:{},onlyId:{},fqn:{}", response.getOperation(), request.get("onlyId"), request.get("fqn"));
            } else {
                acknowledgement.ack();
            }
        } catch (Exception e) {
            log.error("接收到cosmos日志记录失败:{}", JsonUtil.outPutSerialize(request),e);
        }
    }
}
