package com.shuyun.fast.event.config;

import lombok.Getter;

/**
 * @author: <PERSON><PERSON>
 * @date: 2024_12_19
 */
@Getter
public enum AzureKafkaConfigEnum {

    CDP_MEMBER("member", "microsoft-azure/cdp-dev-member-uat.config", "microsoft-azure/cdp-member.config"),
    CDP_ACTIVITY("activity", "microsoft-azure/cdp-dev-activity-uat.config", "microsoft-azure/cdp-activity.config"),
    CDP_ORDER("order", "microsoft-azure/cdp-dev-order-uat.config", "microsoft-azure/cdp-order.config"),
    CDP_MEMBER_DELETE("member_delete", "microsoft-azure/dp-dev-member-delete-uat.config", "microsoft-azure/cdp-member-delete.config"),
    CDP_WECHATOAEVENT("wechatoaevent", "microsoft-azure/cdp-dev-wechatoaevent-uat.config", "microsoft-azure/cdp-wechatoaevent.config"),
    CDP_MASTERDATA("masterdata", "microsoft-azure/cdp-dev-master-data-uat.config", "microsoft-azure/cdp-dev-master-data.config"),
    CDP_WECHATSUBSCRIPTION("wechatsubscription", "microsoft-azure/cdp-dev-wechatsubscription-uat.config", "microsoft-azure/cdp-wechatsubscription.config");

    private String code;

    private String devEnv;

    private String prodEnv;


    AzureKafkaConfigEnum(String code, String devEnv, String prodEnv) {
        this.code = code;
        this.devEnv = devEnv;
        this.prodEnv = prodEnv;
    }

    public static AzureKafkaConfigEnum findByCode(String code) {
        for (AzureKafkaConfigEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
