package com.shuyun.fast.event.config;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 *
 * @author: Jingwei
 * @date: 2024-12-19
 */
@Data
public class AzureKafkaProperties {
    @JsonProperty("bootstrap.servers")
    private String bootstrapServers;
    @JsonProperty("request.timeout.ms")
    private String timeout;
    @JsonProperty("security.protocol")
    private String protocol;
    @JsonProperty("sasl.mechanism")
    private String mechanism;
    @JsonProperty("sasl.jaas.config")
    private String jaasConfig;
}
