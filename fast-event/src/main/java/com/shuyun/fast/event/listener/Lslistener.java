package com.shuyun.fast.event.listener;

import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.event.service.LsService;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.util.JsonUtil;
import io.micronaut.configuration.kafka.annotation.KafkaListener;
import io.micronaut.configuration.kafka.annotation.OffsetReset;
import io.micronaut.configuration.kafka.annotation.Topic;
import io.micronaut.messaging.Acknowledgement;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static io.micronaut.configuration.kafka.annotation.OffsetStrategy.AUTO;
import static io.micronaut.configuration.kafka.annotation.OffsetStrategy.DISABLED;

@Singleton
@Slf4j
public class Lslistener {

    private static final int THREADS = 1;

    @Inject
    private LsService lsService;

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();


    /**
     *  罗森会员信息回调
     *
     * @param request
     */
    @KafkaListener(groupId = "CRM_LS_MEMBER_GROUP", offsetStrategy = AUTO, offsetReset = OffsetReset.LATEST, threads = 6)
    @Topic(value = ModelTags.KAFKA_CRM_LS_MEMBER_INPUT)
    public void lsMemberSync(Map<String, String> request) {
        try {
            log.info("接收到罗森会员信息同步入参:{}", JsonUtil.outPutSerialize(request));
            lsService.lsMemberSync(request);
        } catch (Exception e) {
            log.error("接收到罗森会员信息同步处理失败:{}", JsonUtil.outPutSerialize(request),e);
        }
    }


    /**
     * 罗森回调日志 日志消费
     *
     * @param request
     */
    @KafkaListener(groupId = "KAFKA_LS_LOG_GROUP", offsetStrategy = DISABLED, offsetReset = OffsetReset.LATEST, threads = 6)
    @Topic(value = ModelTags.KAFKA_LS_LOG_INPUT)
    public void lsLogConsumer(Map<String, Object> request, Acknowledgement acknowledgement){
        try {
            log.info("罗森回调日志日志入参:{}", JsonUtil.outPutSerialize(request));
            dataapiHttpSdk.upsert(ModelTags.DATA_FQN_KO_LS_SYNC_LOG, (String) request.get("id"), request, false);
        } catch (Exception e) {
            log.error("罗森回调日志记录失败:{}", JsonUtil.outPutSerialize(request),e);
        }
    }
}
