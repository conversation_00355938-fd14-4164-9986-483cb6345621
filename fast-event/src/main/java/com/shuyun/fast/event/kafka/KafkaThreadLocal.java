package com.shuyun.fast.event.kafka;

import com.shuyun.fast.event.config.AzureKafkaPropertiesLoader;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;

import java.util.Map;

@Slf4j
public class KafkaThreadLocal {

    /**
     * 获取当前线程的生产者列表，如果不存在则创建
     */
    public static KafkaProducer<String, String> getProducer(ThreadLocal<KafkaProducer<String, String>> threadLocal, AzureKafkaPropertiesLoader loader) {
        KafkaProducer<String, String> producer = threadLocal.get();
        if (producer == null) {
            producer = createKafkaProducer(loader.getConfiguration());
            threadLocal.set(producer);
            String threadName = Thread.currentThread().getName();
            log.info("为线程{}创建了1个KafkaProducer实例:{}", threadName, producer.hashCode());
        }
        return producer;
    }




    private static KafkaProducer<String, String> createKafkaProducer(Map<String, Object> config) {
        return new KafkaProducer<>(config);
    }

}
