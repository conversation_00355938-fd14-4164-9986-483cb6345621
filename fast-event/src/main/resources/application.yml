micronaut:
  application:
    name: fast-event
  server:
    port: 8080
    context-path: /fast-event
  executors:
    blocking:
      type: fixed
      number-of-threads: 64
    consumer:
      type: fixed
      number-of-threads: 256
  caches:
    first-biz-cache:
      charset: UTF-8
      maximum-size: 1000
#      expire-after-access: 10s
    second-biz-cache:
      charset: UTF-8
      maximum-size: 1000
#      expire-after-access: 10s
    first-project-cache:
      charset: UTF-8
      maximum-size: 5000
#      expire-after-access: 10s
    second-project-cache:
      charset: UTF-8
      maximum-size: 5000
#      expire-after-access: 10s
    selector-cache:
      charset: UTF-8
      maximum-size: 200
      expire-after-access: 10m
  openapi:
    views:
      #编译期生成swagger-ui所需文件
      spec: swagger-ui.enabled=true
  router:
    static-resources:
      swagger:
        paths: classpath:META-INF/swagger
        mapping: /swagger/**
      swagger-ui:
        paths: classpath:META-INF/swagger/views/swagger-ui
        mapping: /swagger-ui/**

  http:
    client:
      ssl:
        insecure-trust-all-certificates: true

logger:
  config: logback.xml