kind: Service
specVersion: v4
metadata:
  name: fast-event
  apiVersion: v1
  accessPoint:
    container: fast/fast-event
  middleware:
    mysql: {}

containers:
  - name: fast/fast-event
    imagePullPolicy: PullIfNotPresent
    ports:
      - name: fast-event
        containerPort: 8080
        targetPort: 0
        protocol: tcp
profiles:
  - mem: 4096
    cpu: 0.5
    name: default
    replicas: 1
    envs:
      profileActive: dev
    containers:
      - mem: 4096
        cpu: 0.5
        name: fast/fast-event