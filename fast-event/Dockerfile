FROM hub.shuyun.com/base/java:openjdk-11.0.20

WORKDIR /fast-event
COPY target/fast-event.jar /fast-event/fast-event.jar
COPY src/main/resources/application.properties /fast-event/application.properties
COPY src/main/resources/application-dev.yml /fast-event/application-dev.yml
COPY src/main/resources/application-spectrum.yml /fast-event/application-spectrum.yml

ENV spectrum.disableAutoUploadProperties=true

EXPOSE 8080

CMD ["java", "-Dmicronaut.environments=spectrum", "-XX:HeapDumpPath=/var/log/fast-event/heap.hprof", "-XX:ParallelGCThreads=4", "-XX:-CICompilerCountPerCPU", "-XX:CICompilerCount=4", "-XX:NewRatio=1", "-Xms1228m",  "-jar", "fast-event.jar"]
