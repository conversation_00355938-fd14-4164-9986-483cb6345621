# fast-integration

    fast integration kylin crm product modules and provide standard tools

### 注意事项
#### 1、micronaut框架3.10.1版本http参数校验需使用javax.validation:validation-api包注解(micronaut框架4.0版本以上请使用jakarta.validation:validation-api包注解)

#### 2、api接口命名规范:kylin.crm.业务名词.动词

#### 3、多版本包命名规范:xxx.名词.vX_Y_Z,其中X_Y_Z代表api接口x.y.z版本类, 例如handler.v1_0_0

#### 4、不同版本接口相关类名不要携带版本号,一律采用版本包区分

#### 5、api接口响应码响应信息需在messages_zh.properties统一管理,不要在代码中随意log输出

#### 6、对外接口时间字段入参出参统一使用北京时区LocalDateTime,数据模型时间字段请统一使用DateTime

#### 7、本地开发环境VM参数
    -DAPI_VERSION=v1
    -Dspectrum.disableAutoUploadProperties=true
    -DAPP_VERSION=1.0.0
    -DMESOS_TASK_ID=123
    -DSANDBOX=base
    -DSERVICE_NAME=fast-service
    -DENVIRONMENT=vl7y8cbe33
    -Dsystem.config.address=spectrum-vdofqvbe7v.kylin.shuyun.com
    -Dspectrum.key=vl7y8cbe33
    -Dspectrum.secret=10E0DFD44E3044EA8D42038FFD87A559
    -Dmicronaut.environments=dev

#### 8、卡券项目缓存刷新策略:启动全量加载+增量监听事件服务更新缓存,为避免因为卡券项目配置的选择器数据量过大出现服务oom,对单个选择器缓存数据限制1w条.有特殊场景需要超过此限制,随时联系沟通解决方案

#### 9、需要监听事件服务的应用请在配置中心应用目录下(如/fast-service)配置如下2个参数
    system.eventService.userName=应用名称(如fast-service或fast-job或fast-event或其他)
    system.eventService.secret=事件订阅管理菜单下应用账户名对应的密钥(事件订阅管理->找到应用对应账户->查看密钥)

#### 10、卡券项目自定义属性默认动态扩展到CouponProject对象,如果想统一设置到extData集合,请设置getExtData方法@JsonAnyGetter(enabled = false)

#### 11、低版本的dataapi级联保存会删除退单子订单表orderitem关联字段为空的记录,使用订单接口请保证dataapi在1.42.1版本之上

#### 12、通过@Client客户端服务发现方式调用产品接口默认超时时间为10s,可在yml文件通过 micronaut.http.client.read-timeout 配置参数修改(样例值:10s)

#### 20、支持spring体系的jdbcTemplate模版查询能力,使用方式和spring一致直接@Inject或构造注入JdbcTemplate后使用对应方法查询

#### 21、如需新增调用产品接口且使用到了产品SDK中的参数类，需要在IntrospectedClass类中@Introspected注解中新增所使用到的参数类配置

#### 22、序列化反序列化统一采用jackson,禁止使用阿里的fastjson