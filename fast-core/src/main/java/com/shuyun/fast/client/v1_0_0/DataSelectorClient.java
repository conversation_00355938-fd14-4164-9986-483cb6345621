package com.shuyun.fast.client.v1_0_0;

import com.shuyun.fast.domain.v1_0_0.SelectorYaql;
import com.shuyun.ticket.benefit.domain.BenefitProduct;
import com.shuyun.ticket.benefit.domain.BenefitProject;
import com.shuyun.ticket.benefit.vo.Page;
import com.shuyun.ticket.benefit.vo.request.project.ProjectQueryVo;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.*;
import io.micronaut.http.client.annotation.Client;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Client(id = "data-selector", path = "/data-selector/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface DataSelectorClient {
    @Get("/api/detailSelector/rule/{id}/yaql")
    @Consumes(MediaType.APPLICATION_JSON)
    SelectorYaql select(@NotNull @PathVariable("id")String id);
}
