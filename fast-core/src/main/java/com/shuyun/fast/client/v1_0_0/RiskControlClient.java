package com.shuyun.fast.client.v1_0_0;

import com.shuyun.fast.v1_0_0.param.checklist.RiskControlChecklistAddRequest;
import com.shuyun.fast.v1_0_0.param.checklist.RiskControlChecklistDeleteRequest;
import com.shuyun.fast.v1_0_0.param.checklist.RiskControlChecklistGetResponse;
import io.micronaut.core.convert.format.Format;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.*;
import io.micronaut.http.client.annotation.Client;

import javax.validation.Valid;
import java.util.List;

@Client(id = "risk-control", path = "/risk-control/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface RiskControlClient {

    @Put("/api/checklist")
    @Consumes(MediaType.APPLICATION_JSON)
    void addChecklist(@Valid @Body RiskControlChecklistAddRequest request);

    @Get("/api/checklist")
    @Consumes(MediaType.APPLICATION_JSON)
    RiskControlChecklistGetResponse getChecklist(@QueryValue("checklistType") String checklistType,
                                                 @QueryValue("fqn") String fqn,
                                                 @QueryValue("groupIds") @Format("MULTI") List<String> groupIds,
                                                 @QueryValue("customer") String customer);
    @Delete("/api/checklist")
    @Consumes(MediaType.APPLICATION_JSON)
    void deleteChecklist(@Valid @Body RiskControlChecklistDeleteRequest request);
}
