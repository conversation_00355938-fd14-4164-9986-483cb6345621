package com.shuyun.fast.client.v1_0_0;

import com.shuyun.fast.v1_0_0.domain.Point;
import com.shuyun.loyalty.sdk.api.model.http.PlanResponse;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeModifyRequest;
import com.shuyun.loyalty.sdk.api.model.http.points.*;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.*;
import io.micronaut.http.client.annotation.Client;
import io.swagger.v3.oas.annotations.Operation;
import org.jetbrains.annotations.NotNull;
import javax.validation.Valid;
import java.util.List;

@Client(id = "loyalty-facade", path = "/loyalty-facade/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface LoyaltyFacadeClient {
    @Operation(summary = "积分账户初始化")
    @Post("/point:init")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberPointInitResponse init(@Valid @Body MemberPointInitRequest request);

    @Operation(summary = "增加会员积分")
    @Post("/point:send")
    @Consumes(MediaType.APPLICATION_JSON)
    MemberPointSendResponse send(@Valid @Body MemberPointSendRequest request);

    @Operation(summary = "扣减会员积分")
    @Post("/point:deduct")
    @Consumes(MediaType.APPLICATION_JSON)
    void deduct(@Valid @Body MemberPointDeductRequest request);

    @Operation(summary = "允许负积分扣减(当单抵现场景)")
    @Post("/point:allowedNegativeDetect")
    @Consumes(MediaType.APPLICATION_JSON)
    void allowedNegativeDeduct(@Valid @Body MemberPointDeductRequest request);

    @Operation(summary = "冻结会员积分")
    @Post("/point:frozen")
    @Consumes(MediaType.APPLICATION_JSON)
    void frozen(@Valid @Body MemberPointFreezeRequest request);

    @Operation(summary = "解冻会员积分")
    @Post("/point:unfrozen")
    @Consumes(MediaType.APPLICATION_JSON)
    void unfrozen(@Valid @Body MemberPointUnfreezeRequest request);

    @Operation(summary = "消耗已冻结会员积分")
    @Post("/point:frozenDeduct")
    @Consumes(MediaType.APPLICATION_JSON)
    void  frozenDeduct(@Valid @Body MemberPointUseFrozenRequest request);

    @Operation(summary = "会员积分查询")
    @Get("/point")
    @Consumes(MediaType.APPLICATION_JSON)
    Point queryPoint(@QueryValue("memberId") @NotNull String memberId,
                     @QueryValue("pointAccountId") long pointAccountId);

    @Operation(summary = "积分变更撤销")
    @Post("/point:revert")
    @Consumes(MediaType.APPLICATION_JSON)
    void revert(@Valid @Body MemberPointRevertRequest request);

    @Operation(summary = "等级变更")
    @Post("/grade/modify")
    @Consumes(MediaType.APPLICATION_JSON)
    void gradeModify(@Valid @Body MemberGradeModifyRequest request);

    @Operation(summary = "积分导入")
    @Post("/point:import:save-record")
    @Consumes(MediaType.APPLICATION_JSON)
    void importRecords(@QueryValue("importId") String importId,
                       @QueryValue("pointAccountTypeId") Long pointAccountTypeId,
                       @QueryValue("planId") Long planId,
                       @QueryValue("overrideHistoryPoint") Boolean overrideHistoryPoint,
                       @Valid @Body List<MemberPointImportRequest> request);

    @Operation(summary = "积分导入应用")
    @Post("/point:import:apply-record")
    @Consumes(MediaType.APPLICATION_JSON)
    void applyRecords(@QueryValue("importId") String importId,
                      @QueryValue("pointAccountTypeId") Long pointAccountTypeId);

    @Operation(summary = "计划方案信息")
    @Get("/plan:info")
    @Consumes(MediaType.APPLICATION_JSON)
    PlanResponse planInfo(@QueryValue("planId") Long planId);
}
