package com.shuyun.fast.client.v1_0_0;
import com.shuyun.fast.annotation.Api;
import com.shuyun.ticket.benefit.domain.BenefitProduct;
import com.shuyun.ticket.benefit.domain.BenefitProject;
import com.shuyun.ticket.benefit.vo.Page;
import com.shuyun.ticket.benefit.vo.request.project.ProjectQueryVo;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.*;
import io.micronaut.http.client.annotation.Client;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Client(id = "benefit-mgmt", path = "/benefit-mgmt/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface BenefitMgmtClient {
    @Operation(summary = "卡券项目列表")
    @Post("/api/project/list?_i18n_query_=translate")
    @Consumes(MediaType.APPLICATION_JSON)
    @Api
    Page<Map> projectList(@Valid @Body ProjectQueryVo request);

    @Operation(summary = "卡券项目详情")
    @Get("/api/project/{id}?_i18n_query_=translate")
    @Consumes(MediaType.APPLICATION_JSON)
    @Api
    BenefitProject projectDetail(@PathVariable("id") @NotNull String projectId);

    @Operation(summary = "卡券模板详情")
    @Get("/internal/template/{id}?_i18n_query_=translate")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitProduct templateDetail(@PathVariable("id") @NotNull String templateId);
}
