package com.shuyun.fast.client.v1_0_0;

import com.shuyun.cdp.common.response.BaseResponse;
import com.shuyun.cdp.tags.request.openapi.CustomerTagQueryRequest;
import com.shuyun.cdp.tags.request.openapi.CustomerTagsOperateRequest;
import com.shuyun.cdp.tags.request.openapi.CustomersTagOperateRequest;
import com.shuyun.cdp.tags.response.openapi.CategoryTreeResponse;
import com.shuyun.cdp.tags.response.openapi.CustomerTagQueryResponse;
import com.shuyun.cdp.tags.response.openapi.TagListResponse;
import com.shuyun.fast.v1_0_0.param.member.MobileEncryptParam;
import com.shuyun.fast.v1_0_0.result.MobileEncryptResult;
import io.micronaut.core.convert.format.Format;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.*;
import io.micronaut.http.client.annotation.Client;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.QueryParam;
import java.util.Set;

@Client(id = "cdp-mgmt", path = "/cdp-mgmt/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface CdpClient {
    @Operation(summary = "标签目录列表")
    @Get("/openapi/{domainCode}/category")
    @Consumes(MediaType.APPLICATION_JSON)
    CategoryTreeResponse getCategory(@NotNull @PathVariable("domainCode") String domainCode,
                                     @QueryValue(value = "categoryId", defaultValue = "0") Long categoryId);

    @Operation(summary = "标签列表")
    @Get("/openapi/{domainCode}/tag")
    @Consumes(MediaType.APPLICATION_JSON)
    TagListResponse getTags(@NotNull @PathVariable("domainCode") String domainCode,
                            @QueryValue( value = "categoryIds") @Format("MULTI") @NotNull Set<Long> categoryIds,
                            @Parameter(description = "当前页,从0开始", example = "0") @QueryValue(value = "pageNo", defaultValue = "0") Integer pageNo,
                            @Parameter(description = "每页记录数 ", example = "20") @QueryValue(value = "pageSize", defaultValue = "20") Integer pageSize);


    @Operation(summary = "多个消费者打标去标同一个标签")
    @Put("/openapi/{domainCode}/customersTag/operate")
    @Consumes(MediaType.APPLICATION_JSON)
    BaseResponse<String> operateCustomersTag(@NotNull @PathVariable("domainCode") String domainCode,
                                             @Valid @Body CustomersTagOperateRequest customersTagOperateRequest);

    @Operation(summary = "单个消费者打标去标多个标签")
    @Put("/openapi/{domainCode}/customerTags/operate")
    @Consumes(MediaType.APPLICATION_JSON)
    BaseResponse<String> operateCustomerTags(@NotNull @PathVariable("domainCode") String domainCode,
                                             @Valid @Body CustomerTagsOperateRequest customerTagsOperateRequest);


    @Operation(summary = "消费者标签查询")
    @Post("/openapi/{domainCode}/customerTags/query")
    @Consumes(MediaType.APPLICATION_JSON)
    CustomerTagQueryResponse getTagsByCustomer(@NotNull @PathVariable("domainCode") String domainCode,
                                               @Valid @Body CustomerTagQueryRequest customerTagQueryRequest);

}
