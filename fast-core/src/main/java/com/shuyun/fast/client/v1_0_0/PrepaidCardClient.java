package com.shuyun.fast.client.v1_0_0;
import com.shuyun.ticket.prepaid.card.vo.CardDetail;
import com.shuyun.ticket.prepaid.card.vo.CardVo;
import com.shuyun.ticket.prepaid.card.vo.query.CardDetailQuery;
import com.shuyun.ticket.prepaid.card.vo.query.CardQuery;
import com.shuyun.ticket.vo.query.PageBean;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Post;
import io.micronaut.http.annotation.Produces;
import io.micronaut.http.client.annotation.Client;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 后续用到再引入其他接口
 */
@Client(id = "prepaid-card-service", path = "/prepaid-card-service/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface PrepaidCardClient {

    @Post("/api/cards/pageQuery")
    @Consumes(MediaType.APPLICATION_JSON)
    PageBean<CardVo> cardQuery(@Valid @NotNull @Body CardQuery query);

    @Post("/api/cards/detailQuery")
    @Consumes(MediaType.APPLICATION_JSON)
    CardDetail detailQuery(@Valid @NotNull @Body CardDetailQuery query);
}
