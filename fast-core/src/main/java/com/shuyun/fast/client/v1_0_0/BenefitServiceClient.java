package com.shuyun.fast.client.v1_0_0;
import com.shuyun.ticket.benefit.domain.BenefitChangeLog;
import com.shuyun.ticket.benefit.vo.request.benefit.BenefitAvailableListRequest;
import com.shuyun.ticket.benefit.vo.request.benefit.BenefitDiscardRequest;
import com.shuyun.ticket.benefit.vo.request.benefit.BenefitUnfreezeRequest;
import com.shuyun.ticket.benefit.vo.request.benefit.batchImport.BenefitBatchImportRequest;
import com.shuyun.ticket.benefit.vo.response.benefit.*;
import com.shuyun.ticket.benefit.vo.request.benefit.*;
import com.shuyun.ticket.benefit.vo.response.benefit.batchImport.BenefitBatchImportResponse;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.*;
import io.micronaut.http.client.annotation.Client;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.Nullable;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

@Client(id = "benefit-service", path = "/benefit-service/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface BenefitServiceClient {

    @Operation(summary = "查询实例详情")
    @Get("/api/domain/benefit")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitResponse instanceById(
            @QueryValue(value = "_i18n_query_", defaultValue = "translate") String i18nQuery,
            @QueryValue(value = "programId") String programId,
            @Nullable @QueryValue("id") String instanceId,
            @QueryValue("code") String code,
            @QueryValue("subjectFqn") String subjectFqn,
            @Nullable @QueryValue("projectId") String projectId);

    @Operation(summary = "查询卡券实例变更记录日志")
    @Get("/internal/domain/benefit/change/log/{benefitId}")
    @Consumes(MediaType.APPLICATION_JSON)
    List<BenefitChangeLog> changeLog(
            @PathVariable(value = "benefitId") String benefitId,
            @QueryValue(value = "_i18n_query_", defaultValue = "translate") String i18nQuery,
            @QueryValue(value = "subjectId") String subjectId,
            @QueryValue(value = "projectId") String projectId,
            @QueryValue(value = "pageNum") Integer pageNum,
            @QueryValue(value = "pageSize") Integer pageSize);

    @Operation(summary = "卡券列表查询")
    @Get("/api/domain/benefit/list")
    @Consumes(MediaType.APPLICATION_JSON)
    List<BenefitResponse> list(
            @QueryValue(value = "_i18n_query_", defaultValue = "translate") String i18nQuery,
            @QueryValue(value = "programId") String programId,
            @QueryValue(value = "subjectFqn") String subjectFqn,
            @Nullable @QueryValue(value = "projectId") String projectId,
            @QueryValue(value = "holder") String holder,
            @Nullable @QueryValue(value = "state") String state,
            @Nullable @QueryValue(value = "expireTime") LocalDateTime expireTime,
            @Nullable @QueryValue(value = "queryParam") String queryParam,
            @QueryValue(value = "pageNum") Integer pageNum,
            @QueryValue(value = "pageSize") Integer pageSize);

    @Operation(summary = "卡券实例列表查询(分页)")
    @Post("/api/domain/benefit/page")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitDynamicResponse<BenefitResponse> pageList(@Valid @Body BenefitQueryListRequest request);

    @Operation(summary = "卡券实例列表总数查询(分页)")
    @Post("/api/domain/benefit/page/total")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitDynamicResponse<Object> pageListTotal(@Valid @Body BenefitQueryListRequest request);

    @Operation(summary = "可用卡券列表")
    @Post("/api/domain/benefit/scene/available_list?_i18n_query_=translate")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitAvailableListResponse availableList(@Valid @Body BenefitAvailableListRequest request);

    @Operation(summary = "创建")
    @Post("/api/domain/benefit/scene/create")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitGrantResponse create(@Valid @Body BenefitCreateRequest request);

    @Operation(summary = "发放")
    @Post("/api/domain/benefit/scene/grant")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitGrantResponse grant(@Valid @Body BenefitGrantRequest request);

    @Operation(summary = "启用")
    @Post("/api/domain/benefit/scene/activate")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitActivateResponse activate(@Valid @Body BenefitActivateRequest request);

    @Operation(summary = "停用")
    @Post("/api/domain/benefit/scene/deactivate")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitDeactivateResponse deactivate(@Valid @Body BenefitDeactivateRequest request);

    @Operation(summary = "使用校验")
    @Post("/api/domain/benefit/scene/check_use")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitUseResponse checkUse(@Valid @Body BenefitUseRequest request);

    @Operation(summary = "核销")
    @Post("/api/domain/benefit/scene/use")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitUseResponse use(@Valid @Body BenefitUseRequest request);

    @Operation(summary = "反核销")
    @Post("/api/domain/benefit/scene/cancel_use")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitCancelUseResponse cancelUse(@Valid @Body BenefitCancelUseRequest request);

    @Operation(summary = "优惠金额计算")
    @Post("/api/domain/benefit/scene/discount_calc")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitDiscountCalcResponse discountCalc(@Valid @Body BenefitDiscountCalcRequest request);

    @Operation(summary = "锁定")
    @Post("/api/domain/benefit/scene/lock")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitLockResponse lock(@Valid @Body BenefitLockRequest request);

    @Operation(summary = "解锁")
    @Post("/api/domain/benefit/scene/unlock")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitUnlockResponse unLock(@Valid @Body BenefitUnlockRequest request);

    @Operation(summary = "转赠")
    @Post("/api/domain/benefit/scene/transfer")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitTransferResponse transfer(@Valid @Body BenefitTransferRequest request);

    @Operation(summary = "受赠")
    @Post("/api/domain/benefit/scene/receive")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitReceiveResponse receive(@Valid @Body BenefitReceiveRequest request);

    @Operation(summary = "退回")
    @Post("/api/domain/benefit/scene/return")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitReturnResponse returns(@Valid @Body BenefitReturnRequest request);

    @Operation(summary = "绑定")
    @Post("/api/domain/benefit/scene/bind")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitBindResponse bind(@Valid @Body BenefitBindRequest request);

    @Operation(summary = "解绑")
    @Post("/api/domain/benefit/scene/unbind")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitUnbindResponse unbind(@Valid @Body BenefitUnbindRequest request);

    @Operation(summary = "变更有效期")
    @Post("/api/domain/benefit/scene/change_effective")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitChangeEffectiveResponse changeEffective(@Valid @Body BenefitChangeEffectiveRequest request);

    @Operation(summary = "生效")
    @Post("/api/domain/benefit/scene/effective")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitEffectiveResponse effective(@Valid @Body BenefitEffectiveRequest request);

    @Operation(summary = "失效")
    @Post("/api/domain/benefit/scene/expire")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitExpireResponse expire(@Valid @Body BenefitExpireRequest request);


    @Operation(summary = "批量导入实例")
    @Post("/api/domain/benefit/import/batch")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitBatchImportResponse importBatch(@Body String request);

    @Operation(summary = "合卡")
    @Post("/api/domain/benefit/scene/merge")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitMergeResponse mergeBenefit(@Valid @Body BenefitMergeRequest request);


    @Operation(summary = "指定主体作废")
    @Post("/api/domain/benefit/scene/discard_subject")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitDiscardResponse discardBenefitsBySubject(@Valid @Body BenefitDiscardSubjectRequest request);

    @Operation(summary = "冻结")
    @Post("/api/domain/benefit/scene/freeze")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitFreezeResponse freeze(@Valid @Body BenefitFreezeRequest request);

    @Operation(summary = "解冻")
    @Post("/api/domain/benefit/scene/unfreeze")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitUnfreezeResponse unFreeze(@Valid @Body BenefitUnfreezeRequest request);

    @Operation(summary = "作废")
    @Post("/api/domain/benefit/scene/discard")
    @Consumes(MediaType.APPLICATION_JSON)
    BenefitDiscardResponse discard(@Valid @Body BenefitDiscardRequest request);
    
}
