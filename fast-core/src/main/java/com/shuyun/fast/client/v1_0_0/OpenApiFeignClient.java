package com.shuyun.fast.client.v1_0_0;


import com.shuyun.fast.v1_0_0.param.member.MemberBingDto;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Get;
import io.micronaut.http.annotation.Produces;
import io.micronaut.http.annotation.QueryValue;
import io.micronaut.http.client.annotation.Client;

import java.util.List;

@Client(id = "openapi", path = "/openapi/v2")
@Produces(MediaType.APPLICATION_JSON)
public interface OpenApiFeignClient {

    @Get("/member/channels")
    @Consumes(MediaType.APPLICATION_JSON)
    List<MemberBingDto> queryListChannels(@QueryValue("memberId") String memberId,
                                          @QueryValue("memberType") String memberType,
                                          @QueryValue(value = "optionalFields",defaultValue = "") List<String> optionalFields);
}
