package com.shuyun.fast.client.v1_0_0;

import com.shuyun.fast.douyin.param.DoudianSyncRequest;
import com.shuyun.fast.douyin.result.EbrandApiResponse;
import com.shuyun.fast.taobao.param.SyncRequest;
import com.shuyun.fast.taobao.result.SyncResponse;
import com.shuyun.fast.v1_0_0.param.member.MobileEncryptParam;
import com.shuyun.fast.v1_0_0.result.MobileEncryptResult;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.*;
import io.micronaut.http.client.annotation.Client;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.Valid;

@Client(id = "ebrand-member", path = "/ebrand-member/v1")
@Produces(MediaType.APPLICATION_JSON)
public interface EbrandMemberClient {
    @Operation(summary = "明文手机号加密")
    @Post("/mobile/encryption")
    @Consumes(MediaType.APPLICATION_JSON)
    MobileEncryptResult encrypt(@Valid @Body MobileEncryptParam request);

    @Operation(summary = "抖店会员信息同步", description = "应用场景: 当用户在商家非抖音平台信息发生变更时,麒麟CRM会调此接口同步会员信息给抖店抖店会员通")
    @Post("/doudian/api/member/batchUpdate")
    @Consumes(MediaType.APPLICATION_JSON)
    EbrandApiResponse douyinSync(@Body DoudianSyncRequest request);

    @Operation(summary = "线下会员信息同步淘宝会员通", description = "应用场景: 用于三方服务将线下会员积分/等级同步到淘宝线上")
    @Post("/tmall/api/sync")
    @Consumes(MediaType.APPLICATION_JSON)
    SyncResponse taobaoSync(@Body SyncRequest request);
}
