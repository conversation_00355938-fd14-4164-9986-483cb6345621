package com.shuyun.fast.support;

import com.shuyun.air.framework.annotation.AttributeFeatures;
import com.shuyun.air.framework.annotation.ValueConstraint;
import com.shuyun.air.framework.domain.Dictionary;
import com.shuyun.air.framework.domain.Enum;
import com.shuyun.air.framework.model.Attribute;
import com.shuyun.air.framework.model.AttributeSet;
import com.shuyun.air.framework.model.Index;
import com.shuyun.air.framework.model.Type;
import com.shuyun.dm.api.domain.*;
import com.shuyun.dm.api.metadata.vo.DataModelVo;
import com.shuyun.dm.api.vo.SimpleModelMapping;
import com.shuyun.dm.metadata.sdk.MetadataSdkFactory;
import com.shuyun.dm.metadata.sdk.client.MetadataHttpSdk;
import com.shuyun.dm.sdk.Options;
import com.shuyun.dm.sdk.exception.SdkException;
import com.shuyun.lite.context.GlobalContext;
import com.shuyun.lite.context.ThreadLocalUserContext;
import com.shuyun.lite.context.UserContext;
import com.shuyun.lite.util.LazyInitThreadSafeHolder;
import com.shuyun.ticket.domain.BaseEntity;
import com.shuyun.ticket.util.DelayedLooper;
import io.swagger.v3.oas.annotations.media.Schema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.time.*;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import static com.shuyun.dm.i18n.Code._151146;
import static com.shuyun.dm.i18n.Code._151147;
import static com.shuyun.lite.concurrent.ThreadUtil.daemon;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonMap;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

public class MetadataSupport {
    private static final Logger LOGGER = LoggerFactory.getLogger(MetadataSupport.class);

    private static final LazyInitThreadSafeHolder<MetadataHttpSdk> HOLDER = new LazyInitThreadSafeHolder<>(() -> get());

    private static MetadataHttpSdk get() {
        MetadataSdkFactory.INSTANCE.userContextSupplier(() -> {
            UserContext uc = ThreadLocalUserContext.currentUserContext();
            return null != uc ? uc : new UserContext.DefaultUserContext();
        });

        return MetadataSdkFactory.INSTANCE.createMetadataHttpSdk(Options.Companion.newBuilder().build(), null, b -> {
        });
    }

    /**
     * 获取SDK实例
     *
     * @return SDK实例
     */
    public static MetadataHttpSdk instance() {
        return HOLDER.get();
    }

    private static void doMigrate(Supplier<List<InputStream>> scriptSupplier, Consumer<String> successCallback) {
        LOGGER.info("开始执行元数据变更");
        long st = System.currentTimeMillis();

        GlobalContext.tenantProvider().callbackWithRetry(tenantId -> {
            HOLDER.get().migrate(scriptSupplier.get());
            successCallback.accept(tenantId);
        }, null, "执行元数据变更", true, false);

        LOGGER.info("元数据变更执行完成，耗时{}毫秒", (System.currentTimeMillis() - st));
    }

    /**
     * 元数据变更
     *
     * @param scriptSupplier  元数据变更脚本提供者
     * @param successCallback 元数据变更成功回调
     */
    public static void migrate(Supplier<List<InputStream>> scriptSupplier, Consumer<String> successCallback) {
        daemon(() -> {
            new DelayedLooper().loopOnException(() -> {
                doMigrate(scriptSupplier, successCallback);
            }, (t, retryInterval) -> {
                LOGGER.error("元数据变更执行异常，休眠{}秒后重试", retryInterval / 1000, t);
            });
        }, "元数据变更执行器").start();
    }

    /**
     * 创建或更新模型
     *
     * @param models 模型列表
     */
    public static void migrate(Collection<DataModel> models) {
        models.forEach(v -> {
            long st = System.currentTimeMillis();
            new DelayedLooper().loopOnException(() -> {
                DataModel old = findModel(v.getFqn());
                if (v.getType().isEnum() && null != old) {
                    // 比对模型枚举可选值差异，补充缺失的可选值
                    List<String> exists = null == old.getEnums() ? emptyList()
                            : old.getEnums().stream().map(I18nText::getText).collect(Collectors.toList());
                    List<I18nText> needAddEnums = v.getEnums().stream().filter(o -> !exists.contains(o.getText()))
                            .collect(Collectors.toList());
                    if (needAddEnums.isEmpty()) {
                        LOGGER.info("模型已存在，无需创建，模型FQN：{}", v.getFqn());
                    } else {
                        instance().updateModel(v.getFqn(), v);

                        LOGGER.info("模型更新成功，模型FQN：{}，新增枚举数量：{}，耗时{}毫秒", v.getFqn(), needAddEnums.size(),
                                (System.currentTimeMillis() - st));
                    }
                } else if (null != old) {
                    // 比对模型属性列表差异，补充缺失的属性
                    List<String> exists = null == old.getFields() ? emptyList()
                            : old.getFields().stream().map(ModelField::getName).collect(Collectors.toList());
                    List<ModelField> needAddFields = v.getFields().stream().filter(o -> !exists.contains(o.getName()))
                            .collect(Collectors.toList());
                    if (needAddFields.isEmpty()) {
                        LOGGER.info("模型已存在，无需创建，模型FQN：{}", v.getFqn());
                    } else {
                        instance().batchAddField(v.getFqn(), needAddFields);

                        LOGGER.info("模型更新成功，模型FQN：{}，新增属性数量：{}，耗时{}毫秒", v.getFqn(), needAddFields.size(),
                                (System.currentTimeMillis() - st));
                    }
                } else {
                    instance().createModel(v);

                    LOGGER.info("模型创建成功，模型FQN：{}，耗时{}毫秒", v.getFqn(), (System.currentTimeMillis() - st));
                }
            }, (t, retryInterval) -> {
                LOGGER.error("初始化模型异常，模型FQN：{}，休眠{}秒后重试", v.getFqn(), retryInterval / 1000, t);
            });
        });
    }

    /**
     * 根据FQN查找模型
     *
     * @param fqn 模型FQN
     * @return 如果模型存在，则返回对应的模型，否则返回null
     */
    public static DataModel findModel(String fqn) {
        try {
            return HOLDER.get().getModel(fqn);
        } catch (SdkException e) {
            if (_151146.equals(e.getError_code()) || _151147.equals(e.getError_code())) {
                return null;
            }
            throw e;
        }
    }

    /**
     * 根据FQN查找模型属性（含衍生属性）
     *
     * @param fqnList 模型FQN列表
     * @return 如果模型存在，则返回对应的模型属性，否则返回null
     */
    public static Map<String, Set<String>> modelFields(Set<String> fqnList) {
        Map<String, Set<String>> fieldMap = new LinkedHashMap<>();
        HOLDER.get().getCachedModels(new LinkedList<>(fqnList)).getData().forEach(model -> {
            Set<String> fieldNames = new LinkedHashSet<>();
            if (null != model.getFields()) {
                model.getFields().forEach(f -> fieldNames.add(f.getName()));
            }
            if (null != model.getDerivedFields()) {
                model.getDerivedFields().forEach(f -> fieldNames.add(f.getName()));
            }
            fieldMap.put(model.getFqn(), fieldNames);
        });
        return fieldMap;
    }

    /**
     * 根据FQN查找枚举值列表
     *
     * @param fqn 模型FQN
     * @return 枚举值列表
     */
    public static Map<String, Boolean> enumValues(String fqn) {
        Map<String, Boolean> values = new LinkedHashMap<>();

        DataModelVo m = HOLDER.get().getCachedModel(fqn);
        if (null != m && null != m.getEnums()) {
            m.getEnums().forEach(v -> values.put(v.getText(), null != v.getDisabled() ? v.getDisabled() : false));
        }

        return values;
    }

    /**
     * 根据FQN查找模型映射关系
     *
     * @param fqn 模型FQN
     * @return 模型映射关系
     */
    public static SimpleModelMapping findSimpleModelMapping(String fqn) {
        return HOLDER.get().getSimpleModelMapping(fqn);
    }

    /**
     * 基于类的定义，生成数据模型
     *
     * @param fqnMap   类与模型FQN的映射关系
     * @param superMap 父类映射关系
     * @return 类与模型的映射关系
     */
    public static Map<Class<?>, DataModel> scanModels(Map<Class<?>, String> fqnMap, Map<Class<?>, Class<?>> superMap) {
        Map<Class<?>, DataModel> models = new LinkedHashMap<>();

        for (Map.Entry<Class<?>, String> etr : fqnMap.entrySet()) {
            final Class<?> c = etr.getKey();
            DataModel model = new DataModel();
            // 设置模型信息
            model.setFqn(etr.getValue());
            withTitle(c, model::setTitle, model::setDescription);

            if (com.shuyun.air.framework.domain.Enum.class.isAssignableFrom(c)) {
                // 枚举模型
                modelOfEnum(fqnMap, superMap, c, model);
            } else if (BaseEntity.class.isAssignableFrom(c)) {
                // 对象模型
                modelOfObject(fqnMap, superMap, c, model);
            } else {
                throw new IllegalArgumentException("类型" + c + "不符合规范");
            }

            models.put(c, model);
        }

        return models;
    }

    /**
     * 基于类的定义，设置数据模型
     *
     * @param fqnMap   类与模型FQN的映射关系
     * @param superMap 父类映射关系
     * @param c        类
     * @param model    模型
     */
    private static void modelOfEnum(Map<Class<?>, String> fqnMap, Map<Class<?>, Class<?>> superMap, final Class<?> c,
                                    DataModel model) {
        model.setType(ModelType.Enum);

        // 设置枚举列表
        model.setEnums(new ArrayList<>());
        for (Field f : c.getFields()) {
            if (Modifier.isStatic(f.getModifiers()) && com.shuyun.air.framework.domain.Enum.class.isAssignableFrom(f.getType())) {
                try {
                    com.shuyun.air.framework.domain.Enum v = (Enum) f.get(null);
                    I18nText text = new I18nText();
                    text.setText(v.getCode());
                    if (null != v.getTitle() && !v.getTitle().isEmpty()) {
                        text.set_i18nPayload(singletonMap("zh-CN", v.getTitle()));
                    }

                    model.getEnums().add(text);
                } catch (Throwable t) {
                    throw new IllegalArgumentException("类型" + c + "不符合规范", t);
                }
            }
        }
    }

    /**
     * 基于类的定义，设置数据模型
     *
     * @param fqnMap   类与模型FQN的映射关系
     * @param superMap 父类映射关系
     * @param c        类
     * @param model    模型
     */
    private static void modelOfObject(Map<Class<?>, String> fqnMap, Map<Class<?>, Class<?>> superMap, final Class<?> c,
                                      DataModel model) {
        model.setType(ModelType.Object);

        final Set<Field> fields = new LinkedHashSet<>(), stringFields = new LinkedHashSet<>();
        try {
            Field f = c.getDeclaredField("fieldNames");
            f.setAccessible(true);
            allDeclaredFields(c, ((Set<String>) f.get(null)), fields);
        } catch (Throwable t) {
            throw new IllegalArgumentException("类型" + c + "不符合规范", t);
        }
        try {
            Field f = c.getDeclaredField("objectToStrings");
            f.setAccessible(true);
            allDeclaredFields(c, ((Set<String>) f.get(null)), stringFields);
        } catch (Throwable ignore) {
        }

        // 设置属性列表
        model.setFields(new ArrayList<>());
        for (Field f : fields) {
            ModelField field = new ModelField();
            // 设置属性信息
            field.setName(f.getName());
            withTitle(f, field::setTitle, field::setDescription);
            // 设置属性类型
            Class<?> ft = f.getType();
            DataModel type = new DataModel();
            String typeFqn;
            if (BaseEntity.Default.Fields.id.equals(f.getName())) {
                typeFqn = ModelType.Id.getFqn();
            } else if (String.class.equals(ft) || stringFields.contains(f)) {
                typeFqn = ModelType.String.getFqn();
            } else if (Number.class.isAssignableFrom(ft)) {
                typeFqn = (BigDecimal.class.equals(ft) || Double.class.equals(ft) || Float.class.equals(ft))
                        ? ModelType.Number.getFqn() : ModelType.Integer.getFqn();
            } else if (Boolean.class.equals(ft)) {
                typeFqn = ModelType.Boolean.getFqn();
            } else if (LocalDate.class.equals(ft)) {
                typeFqn = ModelType.Date.getFqn();
            } else if (LocalTime.class.equals(ft)) {
                typeFqn = ModelType.Time.getFqn();
            } else if (LocalDateTime.class.equals(ft) || OffsetDateTime.class.equals(ft) || ZonedDateTime.class
                    .equals(ft)) {
                typeFqn = ModelType.DateTime.getFqn();
            } else if (fqnMap.containsKey(ft)) {
                typeFqn = fqnMap.get(ft);
            } else if (superMap.containsKey(ft) && fqnMap.containsKey(superMap.get(ft))) {
                typeFqn = fqnMap.get(superMap.get(ft));
            } else {
                throw new IllegalArgumentException("类型" + c + "不符合规范" + "，不支持属性类型" + ft);
            }
            type.setFqn(typeFqn);
            field.setFieldType(type);
            // 设置属性特征
            withFeature(f, field);
            // 设置属性约束
            withConstraint(f, field);

            model.getFields().add(field);
        }

        // 设置索引
        final List<Index> indexes = new ArrayList<>();
        allIndexes(c, indexes);
        withIndexes(model, indexes);
    }

    /**
     * 扫描类中的属性列表
     *
     * @param c        类
     * @param includes 需要扫描的属性列表
     * @param fields   扫描到的属性列表
     */
    private static void allDeclaredFields(Class<?> c, Set<String> includes, Set<Field> fields) {
        if (Object.class.equals(c)) {
            return;
        }
        Arrays.stream(c.getDeclaredFields()).filter(o -> includes.contains(o.getName())).forEach(fields::add);
        allDeclaredFields(c.getSuperclass(), includes, fields);
    }

    /**
     * 基于类上的注解，搜集索引信息
     *
     * @param c       类
     * @param indexes 索引列表
     */
    private static void allIndexes(Class<?> c, List<Index> indexes) {
        if (Object.class.equals(c)) {
            return;
        }
        Optional.ofNullable(c.getAnnotation(com.shuyun.air.framework.annotation.AttributeSet.class)).map(o -> o.keys())
                .ifPresent(o -> Arrays.stream(o).filter(i -> i.attrs().length > 0).map(i -> {
                    Index k = new Index();
                    k.setName(i.name());
                    k.setUnique(i.unique());
                    k.setAttrs(Arrays.asList(i.attrs()));
                    return k;
                }).forEach(indexes::add));
        allIndexes(c.getSuperclass(), indexes);
    }

    /**
     * 基于类上的注解，设置模型的显示名称、描述等信息
     *
     * @param c             类
     * @param titleConsumer 显示名称
     * @param descConsumer  描述
     */
    public static void withTitle(Class<?> c, Consumer<String> titleConsumer, Consumer<String> descConsumer) {
        withTitle(c.getAnnotation(Schema.class), titleConsumer, descConsumer);
    }

    /**
     * 基于类属性上的注解，设置模型属性的显示名称、描述等信息
     *
     * @param f             类属性
     * @param titleConsumer 显示名称
     * @param descConsumer  描述
     */
    public static void withTitle(Field f, Consumer<String> titleConsumer, Consumer<String> descConsumer) {
        withTitle(f.getAnnotation(Schema.class), titleConsumer, descConsumer);
    }

    /**
     * 基于注解，设置显示名称、描述等信息
     *
     * @param schema        注解
     * @param titleConsumer 显示名称
     * @param descConsumer  描述
     */
    private static void withTitle(Schema schema, Consumer<String> titleConsumer, Consumer<String> descConsumer) {
        if (null != schema) {
            if (!schema.title().isEmpty()) {
                titleConsumer.accept(schema.title());
            }
            if (!schema.description().isEmpty()) {
                descConsumer.accept(schema.description());
            }
        }
    }

    /**
     * 基于类属性上的注解，设置模型属性的特征信息
     *
     * @param f     类属性
     * @param field 模型属性
     */
    public static void withFeature(Field f, ModelField field) {
        AttributeFeatures af = Optional.ofNullable(f.getAnnotation(com.shuyun.air.framework.annotation.Attribute.class))
                .map(o -> o.features()).orElse(null);
        if (null == af) {
            return;
        }
        field.setI18nEnabled(af.i18nOn() ? Boolean.TRUE : null);
        FieldConstraint constraint = field.getConstraint();
        if (null == constraint) {
            constraint = new FieldConstraint();
            field.setConstraint(constraint);
        }
        constraint.setEncrypted(af.encrypted() ? Boolean.TRUE : null);
    }

    /**
     * 基于类属性上的注解，设置模型属性的约束信息
     *
     * @param f     类属性
     * @param field 模型属性
     */
    public static void withConstraint(Field f, ModelField field) {
        ValueConstraint c = f.getAnnotation(ValueConstraint.class);
        if (null == c) {
            return;
        }
        FieldConstraint constraint = field.getConstraint();
        if (null == constraint) {
            constraint = new FieldConstraint();
            field.setConstraint(constraint);
        }
        constraint.setNullable(!c.nullable() ? Boolean.FALSE : null);
        constraint.setUnique(c.unique() ? Boolean.TRUE : null);
        constraint.setDefaultValue(!c.defaultValue().isEmpty() ? c.defaultValue() : null);
        constraint.setMinValue(!c.min().isEmpty() ? c.min() : null);
        constraint.setMaxValue(!c.max().isEmpty() ? c.max() : null);
        constraint.setMinExclusive(c.exclusiveMin() ? Boolean.TRUE : null);
        constraint.setMaxExclusive(c.exclusiveMax() ? Boolean.TRUE : null);
        constraint.setStringLength((c.maxLen() > 0 && c.maxLen() < Integer.MAX_VALUE) ? c.maxLen() : null);
        constraint.setFractionalLength(c.scale() > 0 ? c.scale() : null);
    }

    /**
     * 基于索引定义，设置模型的索引信息
     *
     * @param model   模型
     * @param indexes 索引定义
     */
    public static void withIndexes(DataModel model, List<Index> indexes) {
        if (null == indexes || indexes.isEmpty()) {
            return;
        }

        final Set<String> exists = new LinkedHashSet<>();
        Optional.ofNullable(model.getTrait()).map(ModelTrait::getCompositeIndexes)
                .ifPresent(o -> o.forEach(i -> exists.add(String.join(",", i.getFields()))));

        final Map<String, Boolean> fieldIndexes = new LinkedHashMap<>();
        final List<CompositeIndex> compositeIndexes = new ArrayList<>();
        for (Index index : indexes) {
            if (index.getAttrs().size() == 1) {
                fieldIndexes.put(index.getAttrs().get(0), index.getUnique());
            } else if (!exists.contains(String.join(",", index.getAttrs()))) {
                CompositeIndex compositeIndex = new CompositeIndex();
                compositeIndex.setFields(index.getAttrs());
                compositeIndex.setUnique(index.getUnique());
                compositeIndexes.add(compositeIndex);
            }
        }
        if (!compositeIndexes.isEmpty()) {
            if (null == model.getTrait()) {
                model.setTrait(new ModelTrait());
            }
            if (null == model.getTrait().getCompositeIndexes()) {
                model.getTrait().setCompositeIndexes(compositeIndexes);
            } else {
                model.getTrait().getCompositeIndexes().addAll(compositeIndexes);
            }
        }

        for (ModelField field : model.getFields()) {
            if (fieldIndexes.containsKey(field.getName())) {
                if (null == field.getConstraint()) {
                    field.setConstraint(new FieldConstraint());
                }
                if (Boolean.TRUE.equals(fieldIndexes.get(field.getName()))) {
                    field.getConstraint().setUnique(Boolean.TRUE);
                } else {
                    field.setIndexed(Boolean.TRUE);
                }
            }
        }
    }

    /**
     * 基于字典集的定义，设置模型的枚举值
     *
     * @param model        模型
     * @param dictionaries 字典集
     */
    public static void withExtends(DataModel model, List<Dictionary> dictionaries) {
        if (null == model.getEnums()) {
            model.setEnums(new ArrayList<>());
        }

        List<String> exists = model.getEnums().stream().map(I18nText::getText).collect(Collectors.toList());
        Optional.ofNullable(dictionaries).orElse(emptyList()).stream()
                .filter(o -> !exists.contains(o.getCode()))
                .forEach(v -> {
                    I18nText text = new I18nText();
                    text.setText(v.getCode());
                    if (null != v.getTitle() && !v.getTitle().isEmpty()) {
                        text.set_i18nPayload(singletonMap("zh-CN", v.getTitle()));
                    }

                    model.getEnums().add(text);
                });
    }

    /**
     * 基于属性集的定义，设置模型的索引、属性信息
     *
     * @param model        模型
     * @param attributeSet 属性集
     */
    public static void withExtends(DataModel model, AttributeSet attributeSet) {
        if (null == attributeSet) {
            return;
        }

        List<String> exists = model.getFields().stream().map(ModelField::getName).collect(Collectors.toList());

        // 设置扩展属性
        attributeSet.getAttributes().values().stream().filter(o -> !exists.contains(o.getCode()))
                .forEach(o -> model.getFields().add(convert(o)));

        // 设置索引信息
        withIndexes(model, attributeSet.getKeys());
    }

    /**
     * 基于属性集的属性定义，生成模型属性
     *
     * @param f 属性集的属性
     * @return 模型属性
     */
    public static ModelField convert(Attribute f) {
        ModelField field = new ModelField();
        // 设置属性信息
        field.setName(isNotEmpty(f.getOutRef()) ? f.getOutRef() : f.getCode());
        field.setTitle(f.getTitle());
        field.setDescription(f.getDescription());
        // 设置属性类型
        DataModel type = new DataModel();
        String typeFqn;
        Type dt = f.getDataType().getType();
        if (dt == Type.STRING) {
            typeFqn = ModelType.String.getFqn();
        } else if (dt == Type.INTEGER) {
            typeFqn = ModelType.Integer.getFqn();
        } else if (dt == Type.DECIMAL) {
            typeFqn = ModelType.Number.getFqn();
        } else if (dt == Type.BOOLEAN) {
            typeFqn = ModelType.Boolean.getFqn();
        } else if (dt == Type.DATE) {
            typeFqn = ModelType.Date.getFqn();
        } else if (dt == Type.DATETIME) {
            typeFqn = ModelType.DateTime.getFqn();
        } else {
            typeFqn = ModelType.String.getFqn();
        }
        type.setFqn(typeFqn);
        field.setFieldType(type);
        // 设置属性特征
        withFeature(f, field);
        // 设置属性约束
        withConstraint(f, field);

        return field;
    }

    /**
     * 基于类属性上的注解，设置模型属性的特征信息
     *
     * @param f     类属性
     * @param field 模型属性
     */
    public static void withFeature(Attribute f, ModelField field) {
        com.shuyun.air.framework.model.AttributeFeatures af = f.getFeatures();
        if (null == af) {
            return;
        }
        field.setI18nEnabled(af.i18nOn() ? Boolean.TRUE : null);
        FieldConstraint constraint = field.getConstraint();
        if (null == constraint) {
            constraint = new FieldConstraint();
            field.setConstraint(constraint);
        }
        constraint.setEncrypted(Boolean.TRUE.equals(af.getEncrypted()) ? Boolean.TRUE : null);
    }

    /**
     * 基于类属性上的注解，设置模型属性的约束信息
     *
     * @param f     类属性
     * @param field 模型属性
     */
    public static void withConstraint(Attribute f, ModelField field) {
        com.shuyun.air.framework.model.ValueConstraint c = f.getDataType().getConstraint();
        if (null == c) {
            return;
        }
        FieldConstraint constraint = field.getConstraint();
        if (null == constraint) {
            constraint = new FieldConstraint();
            field.setConstraint(constraint);
        }
        constraint.setNullable(c.getNullable());
        constraint.setUnique(c.getUnique());
        constraint.setDefaultValue(
                null != c.getDefaultValue() && !c.getDefaultValue().isEmpty() ? c.getDefaultValue() : null);
        constraint.setMinValue(null != c.getMin() && !c.getMin().isEmpty() ? c.getMin() : null);
        constraint.setMaxValue(null != c.getMax() && !c.getMax().isEmpty() ? c.getMax() : null);
        constraint.setMinExclusive(c.getExclusiveMin());
        constraint.setMaxExclusive(c.getExclusiveMax());
        constraint.setStringLength(c.getMaxLen());
        constraint.setFractionalLength(c.getScale());
    }
}
