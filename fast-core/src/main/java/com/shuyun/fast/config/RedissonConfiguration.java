package com.shuyun.fast.config;

import io.micronaut.context.annotation.ConfigurationProperties;
import io.micronaut.core.annotation.Nullable;
import lombok.Data;
@Data
@ConfigurationProperties("fast.redis")
public class RedissonConfiguration {
    private Boolean enable;
    private String ssl;
    private String address;
    @Nullable
    private String password;
    private String poolSize = "64";
    private String database = "0";
    private int nettyThread = 64;

}
