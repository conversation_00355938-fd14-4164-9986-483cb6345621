package com.shuyun.fast.service.v1_0_0;

import com.shuyun.fast.util.ModelUtil;
import io.micronaut.core.annotation.Order;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import java.util.Collections;

@Slf4j
@Singleton
@Order(-99)
public class ModelService {
    @PostConstruct
    public void init() {
        log.info("data model init start...");
        ModelUtil.initModel(null, Collections.emptyMap());
        log.info("data model init end...");
    }
}
