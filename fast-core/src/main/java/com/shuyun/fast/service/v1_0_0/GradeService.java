package com.shuyun.fast.service.v1_0_0;


import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.cache.v1_0_0.GradeCache;
import com.shuyun.fast.client.v1_0_0.LoyaltyFacadeClient;
import com.shuyun.fast.client.v1_0_0.LoyaltyManagerClient;
import com.shuyun.fast.entity.BizCache;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.loyalty.SortType;
import com.shuyun.fast.util.DateUtil;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.domain.*;
import com.shuyun.fast.v1_0_0.param.grade.GradeBudgetParam;
import com.shuyun.fast.v1_0_0.param.grade.GradeGetParam;
import com.shuyun.fast.v1_0_0.param.grade.GradeMetadataParam;
import com.shuyun.fast.v1_0_0.param.grade.GradeRecordsGetParam;
import com.shuyun.fast.v1_0_0.param.medal.MedalGetParam;
import com.shuyun.fast.v1_0_0.result.GradeBudgetResult;
import com.shuyun.fast.v1_0_0.result.GradeMetadataResult;
import com.shuyun.lite.context.GlobalContext;
import com.shuyun.loyalty.api.constants.GradeRecordType;
import com.shuyun.loyalty.api.request.BudgetGradeRequest;
import com.shuyun.loyalty.api.response.BudgetGradeResponse;
import com.shuyun.loyalty.api.response.MemberMedalResponse;
import com.shuyun.loyalty.sdk.api.model.Page;
import com.shuyun.loyalty.sdk.api.model.http.GradeDefinitionResponse;
import com.shuyun.loyalty.sdk.api.model.http.MedalDefinitionResponse;
import com.shuyun.loyalty.sdk.api.model.http.PlanResponse;
import com.shuyun.loyalty.sdk.api.model.http.SubjectResponse;
import com.shuyun.loyalty.sdk.api.model.http.grade.GradeRuleGroupApiVo;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeRecordResponse;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeResponse;
import com.shuyun.loyalty.sdk.api.model.http.medal.MedalRuleGroupApiVo;
import io.micronaut.cache.DefaultCacheManager;
import io.micronaut.cache.SyncCache;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

@Singleton
@Slf4j
public class GradeService {
    private final LoyaltyManagerClient managerClient;
    private final LoyaltyFacadeClient facadeClient;
    private static final String tenantId = GlobalContext.defTenantId();
    private final BizCacheService bizCacheService;
    private final SyncCache gradeMetaCache;
    private final SyncCache medalMetaCache;

    public GradeService(DefaultCacheManager cacheManager,
                        LoyaltyManagerClient managerClient,
                        LoyaltyFacadeClient facadeClient,
                        BizCacheService bizCacheService){
        this.managerClient = managerClient;
        this.facadeClient = facadeClient;
        this.bizCacheService = bizCacheService;
        this.gradeMetaCache = cacheManager.getCache("gradeMetaCache");
        this.medalMetaCache = cacheManager.getCache("medalMetaCache");
    }

    public GradeCache bizCacheGet(ApiBaseParam param, String gradeBizType){
        GradeCache cache = bizCacheService.get(GradeCache.class, tenantId, param.getBizCode(), BizCache.GRADE)
                .stream()
                .filter(c->gradeBizType.equals(c.getGradeBizType()))
                .findFirst()
                .orElse(null);
        if(Objects.isNull(cache)){
            throw new ApiException(ApiTags.API_RESP_CODE_500001, BizCache.GRADE);
        }
        return cache;
    }
    public MemberGradeResponse gradeGet(GradeGetParam param){
        String memberId = param.getIdentify().getMemberId();
        String gradeBizType = param.getGradeBizType();
        GradeCache cache = bizCacheGet(param, gradeBizType);
        MemberGradeResponse defaultGrade = new MemberGradeResponse();
        defaultGrade.setCurrentGradeDefinitionId(cache.getGradeId());
        defaultGrade.setGradeDefinitionName(cache.getGradeName());
        return managerClient.findMemberGradeList(0, 20, cache.getGradeHierarchyid(), memberId, null)
                .stream()
                .findFirst()
                .orElse(defaultGrade);
    }

    public Page<MemberGradeRecordResponse> gradeRecordsGet(GradeRecordsGetParam param){
        String memberId = param.getIdentify().getMemberId();
        String gradeBizType = param.getGradeBizType();
        GradeCache cache = bizCacheGet(param, gradeBizType);
        SortType sortType = "ASC".equals(param.getSortType())?SortType.CREATED_ASC:SortType.CREATED_DESC;
        return managerClient.pageMemberGradeRecordList(param.getPage(),
                param.getPageSize(),
                cache.getGradeHierarchyid(),
                memberId,
                null,
                sortType,
                null,
                null,
                null,
                DateUtil.utcTime(param.getStartTime()),
                DateUtil.utcTime(param.getEndTime()));
    }

    public List<Medal> medalGet(MedalGetParam param){
        String memberId = param.getIdentify().getMemberId();
        String gradeBizType = param.getMedalBizType();
        GradeCache cache = bizCacheGet(param, gradeBizType);
        List<MemberMedalResponse> medals = managerClient.memberMedals(0, 100, memberId, cache.getPlanId(), cache.getMedalHierarchyid());
        return medals.stream()
                .map(medal -> {
                    Medal m = new Medal();
                    m.setMemberId(memberId);
                    m.setId(medal.getMedalDefinitionId());
                    m.setName(medal.getMedalDefinitionName());
                    m.setEffectTime(DateUtil.localTime(medal.getEffectDate()));
                    m.setExpiredTime(DateUtil.localTime(medal.getOverdueDate()));
                    return m;
                })
                .collect(Collectors.toList());
    }

    public GradeBudgetResult gradeBudget(GradeBudgetParam param){
        if(StringUtils.isEmpty(param.getGradeId())){
            GradeGetParam gradeGetParam = new GradeGetParam();
            gradeGetParam.setGradeBizType(param.getGradeBizType());
            gradeGetParam.setIdentify(param.getIdentify());
            gradeGetParam.setBizCode(param.getBizCode());
            MemberGradeResponse gradeResponse = gradeGet(gradeGetParam);
            param.setGradeId(String.valueOf(gradeResponse.getCurrentGradeDefinitionId()));
        }
        String memberId = param.getIdentify().getMemberId();
        String gradeBizType = param.getGradeBizType();
        GradeCache cache = bizCacheGet(param, gradeBizType);
        BudgetGradeRequest request = new BudgetGradeRequest();
        request.setMemberId(memberId);
        request.setGradeHierarchyId(cache.getGradeHierarchyid());
        request.setGradeRuleTypes(Arrays.asList(GradeRecordType.UPGRADE));
        request.setTargetGradeIds(Arrays.asList(param.getGradeId()));
        ArrayList<BudgetGradeResponse> list = managerClient.gradeBudget(request);

        List<GradeDefinitionResponse> gradeMeta = getGradeMeta(cache.getPlanId(), cache.getGradeHierarchyid());
        String gradeName = gradeMeta.stream()
                .filter(g->String.valueOf(g.getId()).equals(param.getGradeId()))
                .findFirst()
                .map(GradeDefinitionResponse::getName)
                .orElse(null);

        GradeBudgetResult result = new GradeBudgetResult();
        result.setMemberId(memberId);
        result.setGradeId(param.getGradeId());
        result.setGradeName(gradeName);
        if(CollectionUtils.isEmpty(list)){
            return result;
        }

        GradeRuleGroupApiVo ruleApiVo = getGradeRuleMeta(cache.getGradeHierarchyid(), Long.valueOf(param.getGradeId()), GradeRecordType.UPGRADE.name());
        if(Objects.nonNull(ruleApiVo) && CollectionUtils.isNotEmpty(ruleApiVo.getRuleList())){
            String expression = ruleApiVo.getRuleList().get(0).getExpressionTranslated();
            String displayInfo = ruleApiVo.getRuleList().get(0).getDisplayInfo();
            GradeRuleGroup ruleGroup = extractRule(expression, displayInfo);

            GradeBudgetDifferenceGroup upgradeDifference = new GradeBudgetDifferenceGroup();
            upgradeDifference.setLogicOperator(ruleGroup.getLogicOperator());
            List<GradeBudgetDifference> differences = new ArrayList<>();
            list.forEach(r->{
                BudgetGradeResponse.BudgetGradeRuleResponse rule = r.getGradeRuleList().get(0);
                List<BudgetGradeResponse.BudgetGradeRuleFilterGroupResponse> ruleFilterGroupList = rule.getGradeRule().getFilterGroupList();
                ruleFilterGroupList.forEach(g->{
                    GradeBudgetDifference difference = new GradeBudgetDifference();
                    BudgetGradeResponse.BudgetGradeRuleFilterResponse ruleFilterResponse = g.getFilterList().get(0);
                    difference.setAttributeCode(ruleFilterResponse.getProperty().getPropertyId());
                    difference.setAttributeName(ruleFilterResponse.getProperty().getPropertyName());
                    difference.setGradeThreshold(Long.valueOf((String)ruleFilterResponse.getValue()));
                    if(ruleFilterResponse.getCurrentValue() instanceof Integer){
                        Integer currentValue = (Integer)ruleFilterResponse.getCurrentValue();
                        difference.setAttributeValue(Long.valueOf(currentValue.intValue()));
                    }
                    if(ruleFilterResponse.getCurrentValue() instanceof Double){
                        Double currentValue = (Double)ruleFilterResponse.getCurrentValue();
                        difference.setAttributeValue(Long.valueOf(currentValue.longValue()));
                    }
                    difference.setDifference(difference.getGradeThreshold() - difference.getAttributeValue());
                    differences.add(difference);
                });
            });
            upgradeDifference.setDifferences(differences);

            result.setUpgradeDifference(upgradeDifference);
        }

        return result;
    }
    public List<GradeMetadataResult> gradeMetadataGet(GradeMetadataParam param){
        String gradeBizType = param.getGradeBizType();
        GradeCache cache = bizCacheGet(param, gradeBizType);
        List<GradeMetadataResult> metaList = new ArrayList<>();
        if("GRADE".equals(gradeBizType)){
            List<GradeDefinitionResponse> gradeMeta = getGradeMeta(cache.getPlanId(), cache.getGradeHierarchyid());
            for(GradeDefinitionResponse grade:gradeMeta){
                GradeMetadataResult result = new GradeMetadataResult();
                result.setGradeId(String.valueOf(grade.getId()));
                result.setGradeName(grade.getName());
                result.setGradeSort(grade.getSort().intValue());

                if(param.getRulesRequired()){
                    GradeRuleGroupApiVo upgradeRule = getGradeRuleMeta(cache.getGradeHierarchyid(), grade.getId(), "UPGRADE");
                    if(Objects.nonNull(upgradeRule) && CollectionUtils.isNotEmpty(upgradeRule.getRuleList())){
                        String expression = upgradeRule.getRuleList().get(0).getExpressionTranslated();
                        String displayInfo = upgradeRule.getRuleList().get(0).getDisplayInfo();
                        result.setUpgradeRule(extractRule(expression, displayInfo));
                    }

                    GradeRuleGroupApiVo stayGradeRule = getGradeRuleMeta(cache.getGradeHierarchyid(), grade.getId(), "GRADE_RECALCULATE");
                    if(Objects.nonNull(stayGradeRule) && CollectionUtils.isNotEmpty(stayGradeRule.getRuleList())){
                        String expression = stayGradeRule.getRuleList().get(0).getExpressionTranslated();
                        String displayInfo = stayGradeRule.getRuleList().get(0).getDisplayInfo();
                        result.setStayGradeRule(extractRule(expression, displayInfo));
                    }

                }
                metaList.add(result);
            }
        }
        if("MEDAL".equals(gradeBizType)){
            List<MedalDefinitionResponse> medalMeta = getMedalMeta(cache.getPlanId(), cache.getMedalHierarchyid());
            for(MedalDefinitionResponse medal:medalMeta){
                GradeMetadataResult result = new GradeMetadataResult();
                result.setGradeId(String.valueOf(medal.getId()));
                result.setGradeName(medal.getName());
                result.setGradeSort(medal.getSort().intValue());
                if(param.getRulesRequired()){
                    MedalRuleGroupApiVo keepMedalRule = getMedalRuleMeta(cache.getMedalHierarchyid(), medal.getId(), "KEEP");
                    if(Objects.nonNull(keepMedalRule)){
                        String expression = keepMedalRule.getRuleList().get(0).getExpressionTranslated();
                        String displayInfo = keepMedalRule.getRuleList().get(0).getDisplayInfo();
                        result.setUpgradeRule(extractRule(expression, displayInfo));
                    }
                }
                metaList.add(result);
            }
        }
        return metaList;
    }

    private GradeRuleGroup extractRule(String expression, String displayInfo){
        GradeRuleGroup ruleGroup = new GradeRuleGroup();
        String[] exps = expression.split("\\) or \\(");
        if(exps.length == 2){
            ruleGroup.setLogicOperator("OR");
        }else{
            exps = expression.split("\\) and \\(");
            ruleGroup.setLogicOperator("AND");
        }
        Gson gson = new Gson();
        Type listType = new TypeToken<List<Map>>(){}.getType();
        List<Map> attributes = gson.fromJson(displayInfo, listType);
        Map<Integer, String> attributesMap = new HashMap<>();
        for(Map attribute:attributes){
            attributesMap.put(Double.valueOf(attribute.get("id").toString()).intValue(), attribute.get("name").toString());
        }
        List<GradeRule> rules = new ArrayList<>();
        for(String exp:exps){
            GradeRule rule = new GradeRule();
            exp = exp.replace("(", "")
                    .replace("{", "")
                    .replace("}", "")
                    .replace(")", "");
            rule.setComparisonOperator(">=");
            if(exp.contains(">=") && exp.contains("<")){
                String[] andParts = exp.split(" and ");
                String lowLineStr = andParts[0].contains(">=")?andParts[0]:andParts[1];
                String upperLineStr = andParts[0].contains("<")?andParts[0]:andParts[1];
                String[] lowLineParts = lowLineStr.split(" >= ");
                String[] upperLineParts = upperLineStr.split(" < ");
                rule.setAttributeCode(lowLineParts[0].trim());
                rule.setAttributeName(attributesMap.get(Integer.valueOf(rule.getAttributeCode())));
                rule.setGradeThreshold(Long.valueOf(lowLineParts[1].trim()));
                rule.setNextGradeThreshold(Long.valueOf(upperLineParts[1].trim()));
            }else{
                if(exp.contains(">=")){
                    String[] parts = exp.split(">= ");
                    rule.setAttributeCode(parts[0].trim());
                    rule.setAttributeName(attributesMap.get(Integer.valueOf(rule.getAttributeCode())));
                    rule.setGradeThreshold(Long.valueOf(parts[1].trim()));
                }
            }
            rules.add(rule);
        }
        ruleGroup.setRules(rules);
        return ruleGroup;
    }

    public List<GradeDefinitionResponse> getGradeMeta(Long planId, Long gradeHierarchyId){
        String key = String.join("_", "grade", String.valueOf(gradeHierarchyId));
        List<GradeDefinitionResponse> gradeMeta = (List<GradeDefinitionResponse>)gradeMetaCache.get(key, List.class).orElse(null);
        if(Objects.isNull(gradeMeta)){
            PlanResponse plan = facadeClient.planInfo(planId);
            if(CollectionUtils.isNotEmpty(plan.getSubjects())){
                for(SubjectResponse subject:plan.getSubjects()){
                    gradeMeta = subject.getGradeHierarchies().get(0).getGradeDefinitions();
                    gradeMetaCache.put(key, gradeMeta);

                }
            }
        }
        return gradeMeta;
    }

    public List<MedalDefinitionResponse> getMedalMeta(Long planId, Long medalHierarchyId){
        String key = String.join("_", "medal", String.valueOf(medalHierarchyId));
        List<MedalDefinitionResponse> medalMeta = (List<MedalDefinitionResponse>)medalMetaCache.get(key, List.class).orElse(null);
        if(Objects.isNull(medalMeta)){
            PlanResponse plan = facadeClient.planInfo(planId);
            if(CollectionUtils.isNotEmpty(plan.getSubjects())){
                for(SubjectResponse subject:plan.getSubjects()){
                    medalMeta = subject.getMedalHierarchies().get(0).getMedalDefinitions();
                    medalMetaCache.put(key, medalMeta);
                }
            }
        }
        return medalMeta;
    }

    public GradeRuleGroupApiVo getGradeRuleMeta(Long gradeHierarchyId, Long gradeDefinitionId, String type){
        String key = String.join("_", String.valueOf(gradeHierarchyId), String.valueOf(gradeDefinitionId), type);
        GradeRuleGroupApiVo gradeRuleMeta = (GradeRuleGroupApiVo)gradeMetaCache.get(key, List.class).orElse(null);
        if(Objects.isNull(gradeRuleMeta)){//缓存中不存在,可能已过期
            List<GradeRuleGroupApiVo> gradeMetaList = managerClient.gradeRuleMetadata(true, gradeHierarchyId)
                    .stream()
                    .filter(r->!r.getName().contains("退单重算"))
                    .collect(Collectors.toList());
            log.info("type:{} gradeMetaList:{}", type, JsonUtil.serialize(gradeMetaList));
            if(CollectionUtils.isNotEmpty(gradeMetaList)){
                for(GradeRuleGroupApiVo rule: gradeMetaList){
                    Long ruleGradeId = rule.getGradeDefinitionId() == -1L ? rule.getRuleList().get(0).getDegradeId() : rule.getGradeDefinitionId();
                    String k = String.join("_", String.valueOf(rule.getGradeHierarchyId()), String.valueOf(ruleGradeId), rule.getGroupType().name());
                    gradeMetaCache.put(k, rule);
                    if(ruleGradeId.equals(gradeDefinitionId) && rule.getGroupType().name().equals(type)){
                        gradeRuleMeta = rule;
                    }
                }

            }
        }
        if(Objects.isNull(gradeRuleMeta)){//缓存中依然不存在,该等级无对应规则
            GradeRuleGroupApiVo rule = new GradeRuleGroupApiVo();
            rule.setRuleList(Collections.emptyList());
            gradeRuleMeta = rule;
            gradeMetaCache.put(key, rule);
        }
        return gradeRuleMeta;
    }

    public MedalRuleGroupApiVo getMedalRuleMeta(Long medalHierarchyId, Long medalDefinitionId, String type){
        String key = String.join("_", String.valueOf(medalHierarchyId), String.valueOf(medalDefinitionId), type);
        MedalRuleGroupApiVo medalRuleMeta = (MedalRuleGroupApiVo)medalMetaCache.get(key, List.class).orElse(null);
        if(Objects.isNull(medalRuleMeta)){//缓存中不存在,可能已过期
            List<MedalRuleGroupApiVo> medalMetaList = managerClient.medalRuleMetadata(true, medalHierarchyId);
            if(CollectionUtils.isNotEmpty(medalMetaList)){
                for(MedalRuleGroupApiVo rule: medalMetaList){
                    String k = String.join("_", String.valueOf(rule.getMedalHierarchyId()), String.valueOf(rule.getMedalDefinitionId()), rule.getGroupType().name());
                    medalMetaCache.put(k, rule);
                    if(rule.getMedalDefinitionId().equals(medalDefinitionId) && rule.getGroupType().name().equals(type)){
                        medalRuleMeta = rule;
                    }
                }

            }
        }
        if(Objects.isNull(medalRuleMeta)){//缓存中依然不存在,该等级无对应规则
            MedalRuleGroupApiVo rule = new MedalRuleGroupApiVo();
            rule.setRuleList(Collections.emptyList());
            medalRuleMeta = rule;
            gradeMetaCache.put(key, rule);
        }
        return medalRuleMeta;
    }
}
