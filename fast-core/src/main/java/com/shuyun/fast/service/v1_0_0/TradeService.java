package com.shuyun.fast.service.v1_0_0;

import com.shuyun.dm.api.dataapi.criteria.CriteriaBuilder;
import com.shuyun.dm.api.dataapi.request.QueryDataRequest;
import com.shuyun.dm.api.vo.PageQueryResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.cache.v1_0_0.MdmCache;
import com.shuyun.fast.cache.v1_0_0.TradeCache;
import com.shuyun.fast.entity.BizCache;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.kafka.KafkaSender;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.util.DateUtil;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.util.ModelUtil;
import com.shuyun.fast.v1_0_0.constant.OrderOwnerType;
import com.shuyun.fast.v1_0_0.domain.TradeMainOrder;
import com.shuyun.fast.v1_0_0.domain.TradeMainOrderItem;
import com.shuyun.fast.v1_0_0.domain.TradeOrder;
import com.shuyun.fast.v1_0_0.domain.TradeRefund;
import com.shuyun.fast.v1_0_0.param.trade.TradeGetParam;
import com.shuyun.fast.v1_0_0.param.trade.TradeOrderSyncParam;
import com.shuyun.fast.v1_0_0.param.trade.TradeRefundSyncParam;
import com.shuyun.lite.context.GlobalContext;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Singleton
@Slf4j
public class TradeService {

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
    private static final String tenantId = GlobalContext.defTenantId();
    private final BizCacheService bizCacheService;
    private final KafkaSender kafkaSender;
    public TradeService(BizCacheService bizCacheService,
                        KafkaSender kafkaSender){
        this.bizCacheService = bizCacheService;
        this.kafkaSender = kafkaSender;
    }

    public TradeCache bizCacheGet(ApiBaseParam param){
        TradeCache cache = bizCacheService.get(TradeCache.class, tenantId, param.getBizCode(), BizCache.TRADE)
                .stream()
                .findFirst()
                .orElse(null);
        if(Objects.isNull(cache)){
            throw new ApiException(ApiTags.API_RESP_CODE_500001, BizCache.TRADE);
        }
        return cache;
    }

    public Void orderSync(TradeOrderSyncParam param) {
        Void result = null;
        if(param.getOrder().getOrderOwnerType().equals(OrderOwnerType.MEMBER)){
            result = memberOrder(param);
        }
        //consumer订单模型异步处理
        kafkaSender.send(ModelTags.EVENT_TOPIC_ORDER, param.getOrder().getOrderId(), param);
        return result;
    }

    public Void memberOrder(TradeOrderSyncParam param) {
        TradeOrder order = param.getOrder();
        String memberId = param.getIdentify().getMemberId();
        order.setMemberId(memberId);
        TradeCache cache = bizCacheGet(param);
        Map<String, Object> map = new HashMap<>();
        map.putAll(JsonUtil.filterConvert(order, Map.class, "tradeOrderFilter,tradeOrderItemFilter", "extension"));
        List<Map<String, Object>> orderItems = new ArrayList<>();
        param.getOrder().getOrderItems().forEach(i->{
            Map<String, Object> item = JsonUtil.filterConvert(i, Map.class, "tradeOrderItemFilter", "extension");
            if(CollectionUtils.isNotEmpty(i.getExtension())){
                item.putAll(i.getExtension());
            }
            item.put("memberId", memberId);
            orderItems.add(item);
        });
        map.put("orderItems", orderItems);
        final DataapiWebSocketSdk dataapiWebSocketSdk = dataapiHttpSdk.asDataapiWebSocketSdk();
        try {
            dataapiWebSocketSdk.withinTransaction(()->{
                String orderFqn = order.fqn();
                dataapiWebSocketSdk.upsert(String.format(orderFqn, cache.getMemberType()), order.getId(), map, true);

                Map<String, Object> filter = new HashMap<>();
                filter.put("orderId", order.getOrderId());
                Map<String, Object> params = new HashMap<>();
                params.put("member", Collections.singletonMap("id", memberId));
                dataapiWebSocketSdk.updateByFilter(String.format(orderFqn, cache.getMemberType()), JsonUtil.serialize(filter), params);

                String itemFqn = order.getOrderItems().get(0).fqn();
                dataapiWebSocketSdk.updateByFilter(String.format(itemFqn, cache.getMemberType()), JsonUtil.serialize(filter), params);
                return null;
            });
        } catch (Throwable e) {
            log.error("error:", e);
            throw new ApiException(ApiTags.API_RESP_CODE_500300, e.getLocalizedMessage());
        }

        return null;
    }


    public Void consumerOrder(TradeOrderSyncParam param) {
        TradeOrder order = param.getOrder();
        order.setOrderOwnerType(OrderOwnerType.CONSUMER);
        String userId = param.getIdentify().getUserId();
        order.setCustomerNo(userId);
        String fqn = order.fqn();
        TradeCache cache = bizCacheGet(param);
        Map<String, Object> map = new HashMap<>();
        map.putAll(JsonUtil.filterConvert(order, Map.class, "tradeOrderFilter,tradeOrderItemFilter", "extension"));
        List<Map<String, Object>> orderItems = new ArrayList<>();
        param.getOrder().getOrderItems().forEach(i->{
            i.setOrderOwnerType(OrderOwnerType.CONSUMER);
            Map<String, Object> item = JsonUtil.filterConvert(i, Map.class, "tradeOrderItemFilter", "extension");
            if(CollectionUtils.isNotEmpty(i.getExtension())){
                item.putAll(i.getExtension());
            }
            if(StringUtils.isNotEmpty(order.getMemberId())){
                item.put("memberId", order.getMemberId());
            }
            orderItems.add(item);
        });
        map.put("orderItems", orderItems);
        map.remove("orderCoupons");
        map.remove("pointFlag");
        map.remove("isSend");
        dataapiHttpSdk.upsert(String.format(fqn, cache.getMemberType()), order.getId(), map, true);
        return null;
    }


    public Void refundSync(TradeRefundSyncParam param) {
        Void result = null;
        if(param.getRefund().getOrderOwnerType().equals(OrderOwnerType.MEMBER)){
            result = memberRefund(param);
        }
        //consumer退单模型异步处理
        kafkaSender.send(ModelTags.EVENT_TOPIC_REFUND, param.getRefund().getOrderId(), param);
        return result;
    }

    public Void memberRefund(TradeRefundSyncParam param) {
        TradeRefund refund = param.getRefund();
        String memberId = param.getIdentify().getMemberId();
        refund.setMemberId(memberId);
        TradeCache cache = bizCacheGet(param);
        Map<String, Object> map = new HashMap<>(JsonUtil.filterConvert(refund, Map.class, "tradeRefundFilter,tradeRefundOrderItemFilter", "extension"));
        List<Map<String, Object>> refundItems = new ArrayList<>();
        refund.getRefundOrderItems().forEach(i->{
            Map<String, Object> item = JsonUtil.filterConvert(i, Map.class, "tradeRefundOrderItemFilter", "extension");
            if(CollectionUtils.isNotEmpty(i.getExtension())){
                item.putAll(i.getExtension());
            }
            item.put("memberId", memberId);
            refundItems.add(item);
        });
        map.put("refundOrderItems", refundItems);
        final DataapiWebSocketSdk dataapiWebSocketSdk = dataapiHttpSdk.asDataapiWebSocketSdk();
        try {
            dataapiWebSocketSdk.withinTransaction(()->{
                String refundFqn = refund.fqn();
                dataapiWebSocketSdk.upsert(String.format(refundFqn, cache.getMemberType()), refund.getId(), map, true);

                Map<String, Object> filter = new HashMap<>();
                filter.put("orderId", refund.getOrderId());

                Map<String, Object> params = new HashMap<>();
                params.put("member", Collections.singletonMap("id", memberId));
                if(StringUtils.isNotEmpty(refund.getOriginOrderId())){
                    params.put("order", Collections.singletonMap("id", refund.getOriginOrderId()));
                }
                dataapiWebSocketSdk.updateByFilter(String.format(refundFqn, cache.getMemberType()), JsonUtil.serialize(filter), params);

                String itemFqn = refund.getRefundOrderItems().get(0).fqn();
                //忽略orderItem关联字段
                dataapiWebSocketSdk.updateByFilter(String.format(itemFqn, cache.getMemberType()), JsonUtil.serialize(filter), params);
                return null;
            });
        } catch (Throwable e) {
            log.error("error:", e);
            throw new ApiException(ApiTags.API_RESP_CODE_500301, e.getLocalizedMessage());
        }

        return null;
    }

    public Void consumerRefund(TradeRefundSyncParam param) {
        TradeRefund refund = param.getRefund();
        refund.setOrderOwnerType(OrderOwnerType.CONSUMER);
        String userId = param.getIdentify().getUserId();
        refund.setCustomerNo(userId);
        TradeCache cache = bizCacheGet(param);
        Map<String, Object> map = new HashMap<>(JsonUtil.filterConvert(refund, Map.class, "tradeRefundFilter,tradeRefundOrderItemFilter", "extension"));
        List<Map<String, Object>> refundItems = new ArrayList<>();
        refund.getRefundOrderItems().forEach(i->{
            i.setOrderOwnerType(OrderOwnerType.CONSUMER);
            Map<String, Object> item = JsonUtil.filterConvert(i, Map.class, "tradeRefundOrderItemFilter", "extension");
            if(CollectionUtils.isNotEmpty(i.getExtension())){
                item.putAll(i.getExtension());
            }
            item.remove("totalFee");
            item.put("refundFee", item.get("payment"));
            item.remove("payment");
            item.remove("orderType");
            if(StringUtils.isNotEmpty(refund.getMemberId())){
                item.put("memberId", refund.getMemberId());
            }
            refundItems.add(item);
        });
        map.put("refundOrderItems", refundItems);
        map.remove("pointFlag");
        map.remove("isSend");
        map.remove("totalFee");
        map.put("refundFee", map.get("payment"));
        map.remove("payment");
        map.remove("orderType");
        map.remove("isInternal");
        final DataapiWebSocketSdk dataapiWebSocketSdk = dataapiHttpSdk.asDataapiWebSocketSdk();
        try {
            dataapiWebSocketSdk.withinTransaction(()->{
                String refundFqn = refund.fqn();
                dataapiHttpSdk.upsert(String.format(refundFqn, cache.getMemberType()), refund.getId(), map, true);

                if(StringUtils.isNotEmpty(refund.getOriginOrderId())){
                    Map<String, Object> filter = new HashMap<>();
                    filter.put("orderId", refund.getOrderId());

                    Map<String, Object> params = new HashMap<>();
                    if(StringUtils.isNotEmpty(refund.getOriginOrderId())){
                        params.put("order", Collections.singletonMap("id", refund.getOriginOrderId()));
                    }
                    dataapiWebSocketSdk.updateByFilter(String.format(refundFqn, cache.getMemberType()), JsonUtil.serialize(filter), params);

                    String itemFqn = refund.getRefundOrderItems().get(0).fqn();
                    //忽略orderItem关联字段
                    dataapiWebSocketSdk.updateByFilter(String.format(itemFqn, cache.getMemberType()), JsonUtil.serialize(filter), params);
                }
                return null;
            });
        } catch (Throwable e) {
            log.error("error:", e);
            throw new ApiException(ApiTags.API_RESP_CODE_500301, e.getLocalizedMessage());
        }
        return null;
    }

    public PageResult<TradeMainOrder> get(TradeGetParam param) {
        TradeCache cache = bizCacheGet(param);
        CriteriaBuilder cb = CriteriaBuilder.newBuilder();
        cb.and(CriteriaBuilder.eq("memberId", param.getIdentify().getMemberId()));
        cb.and(CriteriaBuilder.eq("memberType", cache.getMemberType()));
        String orderType = param.getOrderType();
        if(StringUtils.isNotEmpty(orderType)){
            cb.and(CriteriaBuilder.eq("orderType", orderType));
        }
        String orderStatus = param.getStatus();
        if(StringUtils.isNotEmpty(orderStatus)){
            cb.and(CriteriaBuilder.in("orderStatus", orderStatus.split(",")));
        }
        List<String> channels = param.getChannels();
        if(CollectionUtils.isNotEmpty(channels)){
            cb.and(CriteriaBuilder.in("channelType", channels.toArray()));
        }
        if(Objects.nonNull(param.getStartTime())){
            cb.and(CriteriaBuilder.ge(param.getSortBy(), DateUtil.utcTime(param.getStartTime())));
        }
        if(Objects.nonNull(param.getEndTime())){
            cb.and(CriteriaBuilder.le(param.getSortBy(), DateUtil.utcTime(param.getEndTime())));
        }
        if(StringUtils.isNotEmpty(param.getOrderId())){
            cb.and(CriteriaBuilder.eq("orderId", param.getOrderId()));
        }
        QueryDataRequest query = new QueryDataRequest();
        query.setFields(ModelUtil.getFieldNames("", TradeMainOrder.class).replace("orderItems", ModelUtil.getFieldNames("orderItems", TradeMainOrderItem.class)));
        query.setFqn(String.format(param.fqn(), cache.getMemberType()));
        query.setFilter(cb.build());
        query.setOffset(param.getPage() * param.getPageSize());
        query.setLimit(param.getPageSize());
        String sortStr = "{\"%s\":\"%s\"}";
        query.setSort(String.format(sortStr, param.getSortBy(), param.getSortType()));
        query.setWithTotals(true);
        PageQueryResponse pageResponse = dataapiHttpSdk.queryObjects(query);

        PageResult<TradeMainOrder> result = new PageResult<TradeMainOrder>();
        result.setPage(param.getPage());
        result.setPageSize(param.getPageSize());
        result.setTotalCount(Long.valueOf(pageResponse.getTotals()));
        result.setItems(pageResponse.getData().stream()
                .map(m->JsonUtil.outPutConvert(m, TradeMainOrder.class))
                .collect(Collectors.toList()));
        return result;
    }


}
