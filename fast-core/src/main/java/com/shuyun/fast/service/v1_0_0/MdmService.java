package com.shuyun.fast.service.v1_0_0;

import com.shuyun.dm.api.dataapi.criteria.CriteriaBuilder;
import com.shuyun.dm.api.dataapi.request.QueryDataRequest;
import com.shuyun.dm.api.vo.PageQueryResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.cache.v1_0_0.MdmCache;
import com.shuyun.fast.cache.v1_0_0.TradeCache;
import com.shuyun.fast.entity.BizCache;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.util.DateUtil;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.util.ModelUtil;
import com.shuyun.fast.v1_0_0.domain.MdmShop;
import com.shuyun.fast.v1_0_0.param.mdm.MdmOrgSyncParam;
import com.shuyun.fast.v1_0_0.param.mdm.MdmProductSyncParam;
import com.shuyun.fast.v1_0_0.param.mdm.MdmShopListParam;
import com.shuyun.fast.v1_0_0.param.mdm.MdmShopSyncParam;
import com.shuyun.lite.context.GlobalContext;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Singleton
@Slf4j
public class MdmService {

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
    private static final String tenantId = GlobalContext.defTenantId();
    private final BizCacheService bizCacheService;
    public MdmService(BizCacheService bizCacheService){
        this.bizCacheService = bizCacheService;
    }

    public MdmCache bizCacheGet(ApiBaseParam param){
        MdmCache cache = bizCacheService.get(MdmCache.class, tenantId, param.getBizCode(), BizCache.MDM)
                .stream()
                .findFirst()
                .orElse(null);
        if(Objects.isNull(cache)){
            throw new ApiException(ApiTags.API_RESP_CODE_500001, BizCache.MDM);
        }
        return cache;
    }

    public Void shopSync(MdmShopSyncParam param) {
        String fqn = param.fqn();
        MdmCache cache = bizCacheGet(param);
        param.setMemberType(cache.getMemberType());
        Map<String, Object> map = new HashMap<>();
        map.putAll(JsonUtil.filterConvert(param, Map.class, "mdmShopSyncParamFilter",
                "tenantId", "bizCode", "requestChannel", "requestSystem", "transactionId", "extension"));
        if(CollectionUtils.isNotEmpty(param.getExtension())){
            map.putAll(param.getExtension());
        }
        log.info("shop data:{}", JsonUtil.serialize(map));
        dataapiHttpSdk.upsert(String.format(fqn, cache.getMemberType()), param.getId(), map, false);
        return null;
    }

    public PageResult<MdmShop> shopList(MdmShopListParam param) {
        MdmCache cache = bizCacheGet(param);
        CriteriaBuilder cb = CriteriaBuilder.newBuilder();
        cb.and(CriteriaBuilder.eq("memberType", cache.getMemberType()));
        if(Objects.nonNull(param.getStartTime())){
            cb.and(CriteriaBuilder.ge(param.getSortBy(), DateUtil.utcTime(param.getStartTime())));
        }
        if(Objects.nonNull(param.getEndTime())){
            cb.and(CriteriaBuilder.le(param.getSortBy(), DateUtil.utcTime(param.getEndTime())));
        }
        String status = param.getStatus();
        if(StringUtils.isNotEmpty(status)){
            cb.and(CriteriaBuilder.in("status", status.split(",")));
        }
        QueryDataRequest query = new QueryDataRequest();
        query.setFields(ModelUtil.getFieldNames("", MdmShop.class));
        query.setFqn(String.format(param.fqn(), cache.getMemberType()));
        query.setFilter(cb.build());
        query.setOffset(param.getPage() * param.getPageSize());
        query.setLimit(param.getPageSize());
        String sortStr = "{\"%s\":\"%s\"}";
        query.setSort(String.format(sortStr, param.getSortBy(), param.getSortType()));
        query.setWithTotals(true);
        PageQueryResponse pageResponse = dataapiHttpSdk.queryObjects(query);
        PageResult<MdmShop> result = new PageResult<MdmShop>();
        result.setPage(param.getPage());
        result.setPageSize(param.getPageSize());
        result.setTotalCount(Long.valueOf(pageResponse.getTotals()));
        result.setItems(pageResponse.getData().stream()
                .map(m->JsonUtil.outPutConvert(m, MdmShop.class))
                .collect(Collectors.toList()));
        return result;
    }

    public Void productSync(MdmProductSyncParam param) {
        String fqn = param.fqn();
        MdmCache cache = bizCacheGet(param);
        param.setMemberType(cache.getMemberType());
        Map<String, Object> map = new HashMap<>();
        map.putAll(JsonUtil.filterConvert(param, Map.class, "mdmProductSyncParamFilter",
                "tenantId", "bizCode", "requestChannel", "requestSystem", "transactionId", "extension"));
        if(CollectionUtils.isNotEmpty(param.getExtension())){
            map.putAll(param.getExtension());
        }
        dataapiHttpSdk.upsert(String.format(fqn, cache.getMemberType()), param.getId(), map, false);
        return null;
    }

    public Void orgSync(MdmOrgSyncParam param) {
        String fqn = param.fqn();
        MdmCache cache = bizCacheGet(param);
        param.setMemberType(cache.getMemberType());
        Map<String, Object> map = new HashMap<>();
        map.putAll(JsonUtil.filterConvert(param, Map.class, "mdmOrgSyncParamFilter",
                "tenantId", "bizCode", "requestChannel", "requestSystem", "transactionId", "extension"));
        if(CollectionUtils.isNotEmpty(param.getExtension())){
            map.putAll(param.getExtension());
        }
        dataapiHttpSdk.upsert(String.format(fqn, cache.getMemberType()), param.getId(), map, false);
        return null;
    }


}
