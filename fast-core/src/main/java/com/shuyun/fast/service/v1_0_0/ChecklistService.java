package com.shuyun.fast.service.v1_0_0;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.cache.v1_0_0.ChecklistCache;
import com.shuyun.fast.client.v1_0_0.RiskControlClient;
import com.shuyun.fast.entity.BizCache;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.v1_0_0.param.checklist.*;
import com.shuyun.lite.context.GlobalContext;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Objects;

@Singleton
@Slf4j

public class ChecklistService {

    private final RiskControlClient riskControlClient;
    private final BizCacheService bizCacheService;

    private static final String tenantId = GlobalContext.defTenantId();

    public ChecklistService(RiskControlClient riskControlClient,
                            BizCacheService bizCacheService){
        this.riskControlClient = riskControlClient;
        this.bizCacheService = bizCacheService;
    }

    public ChecklistCache bizCacheGet(ApiBaseParam param, String type){
        ChecklistCache cache = bizCacheService.get(ChecklistCache.class, tenantId, param.getBizCode(), BizCache.CHECKLIST)
                .stream()
                .filter(c->type.equals(c.getType()))
                .findFirst()
                .orElse(null);
        if(Objects.isNull(cache)){
            throw new ApiException(ApiTags.API_RESP_CODE_500001, BizCache.CHECKLIST);
        }
        return cache;
    }

    public Void add(ChecklistAddParam param){
        ChecklistCache cache = bizCacheGet(param, param.getChecklistType());
        RiskControlChecklistAddRequest request = new RiskControlChecklistAddRequest();
        request.setChecklistType(param.getChecklistType());
        request.setCustomer(param.getIdentify().getMemberId());
        request.setFqn(cache.getFqn());
        riskControlClient.addChecklist(request);
        return null;
    }

    public RiskControlChecklistGetResponse get(ChecklistGetParam param){
        ChecklistCache cache = bizCacheGet(param, param.getChecklistType());
        return riskControlClient.getChecklist(param.getChecklistType(),
                cache.getFqn(),
                Arrays.asList(param.getGroupId()),
                param.getIdentify().getMemberId());
    }

    public Void delete(ChecklistDeleteParam param){
        ChecklistCache cache = bizCacheGet(param, param.getChecklistType());
        RiskControlChecklistDeleteRequest request = new RiskControlChecklistDeleteRequest();
        request.setChecklistType(param.getChecklistType());
        request.setFqn(cache.getFqn());
        request.setCustomer(param.getIdentify().getMemberId());
        riskControlClient.deleteChecklist(request);
        return null;
    }
}
