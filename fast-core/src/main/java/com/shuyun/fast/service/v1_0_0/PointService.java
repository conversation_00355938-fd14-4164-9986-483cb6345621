package com.shuyun.fast.service.v1_0_0;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.cache.v1_0_0.PointCache;
import com.shuyun.fast.client.v1_0_0.LoyaltyFacadeClient;
import com.shuyun.fast.client.v1_0_0.LoyaltyManagerClient;
import com.shuyun.fast.entity.BizCache;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.loyalty.FSMPointEvent;
import com.shuyun.fast.loyalty.PCStatus;
import com.shuyun.fast.loyalty.PointSortType;
import com.shuyun.fast.util.DateUtil;
import com.shuyun.fast.v1_0_0.domain.Point;
import com.shuyun.fast.v1_0_0.param.point.PointGetParam;
import com.shuyun.fast.v1_0_0.param.point.PointModifyParam;
import com.shuyun.fast.v1_0_0.param.point.PointRecordsGetParam;
import com.shuyun.lite.context.GlobalContext;
import com.shuyun.loyalty.sdk.api.model.http.points.*;
//import com.shuyun.fast.loyalty.MemberPointSendResponse;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Singleton
@Slf4j
public class PointService {

    private final LoyaltyManagerClient managerClient;
    private final LoyaltyFacadeClient facadeClient;
    private static final String tenantId = GlobalContext.defTenantId();
    private final BizCacheService bizCacheService;
    public PointService(LoyaltyManagerClient managerClient,
                        LoyaltyFacadeClient facadeClient,
                        BizCacheService bizCacheService){
        this.managerClient = managerClient;
        this.facadeClient = facadeClient;
        this.bizCacheService = bizCacheService;
    }

    public PointCache bizCacheGet(ApiBaseParam param, String pointBizType){
        PointCache cache = bizCacheService.get(PointCache.class, tenantId, param.getBizCode(), BizCache.POINT)
                .stream()
                .filter(c->pointBizType.equals(c.getPointBizType()))
                .findFirst()
                .orElse(null);
        if(Objects.isNull(cache)){
            throw new ApiException(ApiTags.API_RESP_CODE_500001, BizCache.POINT);
        }
        return cache;
    }

    public Point pointGet(PointGetParam param) {
        PointCache cache = bizCacheGet(param, param.getPointBizType());
        return facadeClient.queryPoint(param.getIdentify().getMemberId(), cache.getPointAccountTypeId());
    }

    public List<MemberPointRecordResponse> pointRecordsGet(PointRecordsGetParam param) {
        PointCache cache = bizCacheGet(param, param.getPointBizType());
        PointSortType sortType = "ASC".equals(param.getSortType())? PointSortType.createdAsc:PointSortType.createdDesc;
        List<String> recordTypes = param.getRecordTypes();
        List<String> sl = param.getStatus();
        // TODO: 2024/3/15  入参时间转换
        return managerClient.findMemberPointRecords(param.getPage(),
                param.getPageSize(),
                cache.getPointAccountTypeId(),
                null,
                CollectionUtils.isEmpty(recordTypes)?null:recordTypes.stream().map(s-> FSMPointEvent.valueOf(s)).collect(Collectors.toList()),
                CollectionUtils.isEmpty(sl)?null:sl.stream().map(s-> PCStatus.valueOf(s)).collect(Collectors.toList()),
                param.getIdentify().getMemberId(),
                param.getShopCode(),
                null,
                DateUtil.utcTime(param.getStartTime()),
                DateUtil.utcTime(param.getEndTime()),
                param.getTraceId(),
                sortType,
                null);
    }

    public Void freeze(MemberPointFreezeRequest param) {
        facadeClient.frozen(param);
        return null;
    }

    public Void unfreeze(MemberPointUnfreezeRequest param) {
        facadeClient.unfrozen(param);
        return null;
    }

    public Void freezeDeduct(MemberPointUseFrozenRequest param) {
        facadeClient.frozenDeduct(param);
        return null;
    }

    public Void modify(PointModifyParam param) {
        return param.getModifyType().equals(FSMPointEvent.SEND.name())?send(param):deduct(param);
    }

    public Void send(PointModifyParam param) {
        MemberPointSendRequest request = new MemberPointSendRequest();
        PointCache cache = bizCacheGet(param, param.getPointBizType());
        request.setPoint(new BigDecimal(param.getPoint()));
        request.setChannelType(param.getRequestChannel());
        request.setMemberId(param.getIdentify().getMemberId());
        request.setActionName(param.getActionName());
        request.setDesc(param.getDescription());
        request.setPointAccountId(cache.getPointAccountTypeId());
        request.setShopId(param.getShopCode());
        request.setUniqueId(param.getTransactionId());
        request.setTriggerId(param.getTransactionId());
        request.setKzzd1(param.getKZZD1());
        request.setKzzd2(param.getKZZD2());
        request.setKzzd3(param.getKZZD3());
        request.setTx(param.getReversible());
        // TODO: 2024/3/15 时间设置
        request.setEffectiveDate(DateUtil.utcTime(param.getEffectTime()));
        request.setOverdueDate(DateUtil.utcTime(param.getExpiredTime()));
        MemberPointSendResponse resp = facadeClient.send(request);
        return null;
    }

    public Void deduct(PointModifyParam param) {
        MemberPointDeductRequest request = new MemberPointDeductRequest();
        PointCache cache = bizCacheGet(param, param.getPointBizType());
        request.setPoint(new BigDecimal(param.getPoint()));
        request.setChannelType(param.getRequestChannel());
        request.setMemberId(param.getIdentify().getMemberId());
        request.setActionName(param.getActionName());
        request.setDesc(param.getDescription());
        request.setPointAccountId(cache.getPointAccountTypeId());
        request.setShopId(param.getShopCode());
        request.setUniqueId(param.getTransactionId());
        request.setTriggerId(param.getTransactionId());
        request.setKzzd1(param.getKZZD1());
        request.setKzzd2(param.getKZZD2());
        request.setKzzd3(param.getKZZD3());
        request.setTx(param.getReversible());
        facadeClient.deduct(request);
        return null;
    }

    public Void revert(MemberPointRevertRequest param) {
        facadeClient.revert(param);
        return null;
    }
}
