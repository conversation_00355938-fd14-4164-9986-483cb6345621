package com.shuyun.fast.validator;


import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.core.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
public class FastValidator {

    /**
     * 三选一非空校验
     * @param p1
     * @param p2
     * @param p3
     * @param names
     * @return
     */

    public static void oneOfThreeRequiredValidate(Object p1, Object p2, Object p3, String... names) {
        boolean p1Empty = p1 instanceof String ? StringUtils.isEmpty((String) p1) : Objects.isNull(p1);
        boolean p2Empty = p2 instanceof String ? StringUtils.isEmpty((String) p2) : Objects.isNull(p2);
        boolean p3Empty = p3 instanceof String ? StringUtils.isEmpty((String) p3) : Objects.isNull(p3);
        if(p1Empty && p2Empty && p3Empty){
            throw new ApiException(ApiTags.API_RESP_CODE_100009, names);
        }
        int noEmptyCount = (p1Empty ? 0 : 1) + (p2Empty ? 0 : 1) + (p3Empty ? 0 : 1);
        if(noEmptyCount != 1){
            throw new ApiException(ApiTags.API_RESP_CODE_100010, names);
        }
    }

    /**
     * 二选一非空校验
     * @param p1
     * @param p2
     * @param names
     * @return
     */

    public static void eitherOrValidate(Object p1, Object p2, String... names) {
        if(p1 instanceof String && p2 instanceof String){
            if(StringUtils.isEmpty((String)p1) && StringUtils.isEmpty((String)p2)){
                throw new ApiException(ApiTags.API_RESP_CODE_100002, names);
            }
            if(StringUtils.isNotEmpty((String)p1) && StringUtils.isNotEmpty((String)p2)){
                throw new ApiException(ApiTags.API_RESP_CODE_100003, names);
            }
        }
        if(!(p1 instanceof String) && p2 instanceof String){
            if(Objects.isNull(p1) && StringUtils.isEmpty((String)p2)){
                throw new ApiException(ApiTags.API_RESP_CODE_100002, names);
            }
            if(Objects.nonNull(p1) && StringUtils.isNotEmpty((String)p2)){
                throw new ApiException(ApiTags.API_RESP_CODE_100003, names);
            }
        }
        if(p1 instanceof String && !(p2 instanceof String)){
            if(StringUtils.isEmpty((String)p1) && (Objects.isNull(p2))){
                throw new ApiException(ApiTags.API_RESP_CODE_100002, names);
            }
            if(StringUtils.isNotEmpty((String)p1) && Objects.nonNull(p2)){
                throw new ApiException(ApiTags.API_RESP_CODE_100003, names);
            }
        }
        if(!(p1 instanceof String) && !(p2 instanceof String)){
            if(Objects.isNull(p1) && Objects.isNull(p2)){
                throw new ApiException(ApiTags.API_RESP_CODE_100002, names);
            }
            if(Objects.nonNull(p1) && Objects.nonNull(p2)){
                throw new ApiException(ApiTags.API_RESP_CODE_100003, names);
            }
        }
    }

    public static void bothRequiredValidate(Object p1, Object p2, String... names){
        if(p1 instanceof String && p2 instanceof String){
            if(StringUtils.isEmpty((String)p1) || StringUtils.isEmpty((String)p2)){
                throw new ApiException(ApiTags.API_RESP_CODE_100004, names);
            }
        }else{
            if(Objects.isNull(p1) || Objects.isNull(p2)){
                throw new ApiException(ApiTags.API_RESP_CODE_100004, names);
            }
        }
    }

    public static void singleEmptyValidate(Object p1, String... names){
        if(p1 instanceof String){
            if(StringUtils.isNotEmpty((String)p1)){
                throw new ApiException(ApiTags.API_RESP_CODE_100005, names);
            }
        } else if(p1 instanceof Collection){
            if(CollectionUtils.isNotEmpty((Collection)p1)){
                throw new ApiException(ApiTags.API_RESP_CODE_100005, names);
            }
        } else{
            if(Objects.nonNull(p1)){
                throw new ApiException(ApiTags.API_RESP_CODE_100005, names);
            }
        }
    }

    public static void singleRequiredValidate(Object p1, String... names){
        if(p1 instanceof String){
            if(StringUtils.isEmpty((String)p1)){
                throw new ApiException(ApiTags.API_RESP_CODE_100006, names);
            }
        } else if(p1 instanceof Collection){
            if(CollectionUtils.isEmpty((Collection)p1)){
                throw new ApiException(ApiTags.API_RESP_CODE_100006, names);
            }
        } else{
            if(Objects.isNull(p1)){
                throw new ApiException(ApiTags.API_RESP_CODE_100006, names);
            }
        }
    }

    public static void timeIntervalValidate(LocalDateTime startTime, LocalDateTime endTime, Long num, TimeUnit tu){
        boolean exceedInterval;
        if(startTime.isAfter(endTime)){
            throw new ApiException(ApiTags.API_RESP_CODE_100007, "startTime", "endTime");
        }
        switch(tu){
            case DAYS:
                exceedInterval = startTime.plusDays(num).isBefore(endTime);
                break;
            case HOURS:
                exceedInterval = startTime.plusHours(num).isBefore(endTime);
                break;
            case MINUTES:
                exceedInterval = startTime.plusMinutes(num).isBefore(endTime);
                break;
            case SECONDS:
                exceedInterval = startTime.plusSeconds(num).isBefore(endTime);
                break;
            default:
                throw new IllegalArgumentException("error time unit");
        }
        if(exceedInterval){
            throw new ApiException(ApiTags.API_RESP_CODE_100008, "startTime", "endTime", String.valueOf(num), String.valueOf(tu));
        }
    }

    public static void main(String[] args){
        LocalDateTime s = LocalDateTime.now();
        LocalDateTime e = s.minusDays(2);
        FastValidator.timeIntervalValidate(s, e, 1L, TimeUnit.DAYS);
    }
}
