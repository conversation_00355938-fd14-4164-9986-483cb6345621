package com.shuyun.fast.interceptor;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.base.ApiBaseParam;
import io.micronaut.aop.InterceptorBean;
import io.micronaut.aop.MethodInterceptor;
import io.micronaut.aop.MethodInvocationContext;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import java.util.Objects;

@Singleton
@InterceptorBean(Api.class)
@Slf4j
public class ApiInterceptor implements MethodInterceptor {
    final private ObjectMapper objectMapper = new ObjectMapper();

    @PostConstruct
    public void init(){
        SimpleFilterProvider defaultProvider = new SimpleFilterProvider();
        SimpleBeanPropertyFilter defaultFilter = SimpleBeanPropertyFilter.serializeAll();
        defaultProvider.addFilter("tradeOrderFilter", defaultFilter);
        defaultProvider.addFilter("tradeOrderItemFilter", defaultFilter);
        defaultProvider.addFilter("tradeRefundFilter", defaultFilter);
        defaultProvider.addFilter("tradeRefundOrderItemFilter", defaultFilter);
        defaultProvider.addFilter("memberModifyParamFilter", defaultFilter);
        defaultProvider.addFilter("mdmOrgSyncParamFilter", defaultFilter);
        defaultProvider.addFilter("mdmProductSyncParamFilter", defaultFilter);
        defaultProvider.addFilter("mdmShopSyncParamFilter", defaultFilter);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.setFilterProvider(defaultProvider);
        objectMapper.registerModule(new JavaTimeModule());
    }

    @Override
    public Object intercept(MethodInvocationContext context) {
        Object param = context.getParameterValueMap().get("param");
        String traceId = "";
        String apiPath = "";
        String apiVersion = "";
        if(Objects.nonNull(param) && param instanceof ApiBaseParam){
            apiPath = ((ApiBaseParam) param).apiName();
            apiVersion = ((ApiBaseParam) param).apiVersion();
            traceId = ((ApiBaseParam) param).getTransactionId();
        }
        try {
            log.info("api path:{} api version:{} traceId:{} api request:{}", apiPath, apiVersion, traceId, objectMapper.writeValueAsString(context.getParameterValueMap()));
        } catch (Exception e){
            log.error("error:", e);
        }

        long startTime = System.currentTimeMillis();
        Object result = context.proceed();
        try {
            log.info("api path:{} api version:{} traceId:{} api response:{}", apiPath, apiVersion, traceId, objectMapper.writeValueAsString(result));
        } catch (Exception e){
            log.error("error:", e);
        }
        long duration = System.currentTimeMillis() - startTime;
        log.info("api 方法:{} 执行时间:{}ms", context.getTargetMethod(), duration);
        return result;
    }
}
