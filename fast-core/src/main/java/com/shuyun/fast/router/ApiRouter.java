package com.shuyun.fast.router;

import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.beans.BeanIntrospection;
import io.micronaut.core.beans.BeanIntrospector;
import io.micronaut.core.util.StringUtils;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Singleton
public class ApiRouter implements Router<String, String>{

    private final static ConcurrentHashMap<String, String>  apiMap = new ConcurrentHashMap<>();

    @Value("${micronaut.server.context-path}")
    private String serviceName;

    public ApiRouter(){

    }

    @PostConstruct
    @Override
    public void init(){
        Collection<BeanIntrospection<Object>> controllers = BeanIntrospector.SHARED.findIntrospections(Controller.class);
        controllers.forEach(c->{
            Class beanClass = c.getBeanType();
            Controller a = (Controller)beanClass.getDeclaredAnnotation(Controller.class);
            String resourcePath = a.value();
            String[] pathArray = resourcePath.split("/");
            String serviceVersion = pathArray[1];

            Method[] methods = beanClass.getDeclaredMethods();
            Arrays.stream(methods).parallel().forEach(m->{
                Api api = m.getAnnotation(Api.class);
                if(Objects.nonNull(api)){
                    String openPath = String.join("/",
                            serviceName, serviceVersion, api.version(), "api", api.name());
                    Post post = m.getAnnotation(Post.class);
                    String methodPath = post.value();
                    String apiPath = String.join("", serviceName, resourcePath, methodPath);
                    apiMap.put(openPath, apiPath);
                }
            });
        });
        log.info("api path init success");
    }

    public String route(String apiName) {
        String path = apiMap.get(apiName);
        if(StringUtils.isEmpty(path)){
            throw new ApiException(ApiTags.API_RESP_CODE_100000);
        }
        return path;
    }

}
