package com.shuyun.fast.router;

import com.shuyun.fast.base.ApiTag;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.tuple.Tuple2;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Singleton
public class ApiHandlerRouter implements Router<AbstractApiHandler, ApiTag> {

    private final static ConcurrentHashMap<Tuple2<String, String>, AbstractApiHandler> handlerMap = new ConcurrentHashMap<>();
    private final List<AbstractApiHandler> handlers;
    public ApiHandlerRouter(List<AbstractApiHandler> handlers){
        this.handlers = handlers;
    }


    @PostConstruct
    @Override
    public void init() {
        Iterator iterator = handlers.iterator();
        while(iterator.hasNext()){
            AbstractApiHandler handler = (AbstractApiHandler)iterator.next();
            String apiVersion = handler.apiVersion();
            String apiName = handler.apiName();
            Tuple2<String, String> apiKey = new Tuple2<>(apiName, apiVersion);
            handlerMap.put(apiKey, handler);
        }

        log.info("api handler mapping success");
    }
    @Override
    public AbstractApiHandler route(ApiTag tag) {
        Tuple2<String, String> apiKey = new Tuple2<>(tag.apiName(), tag.apiVersion());
        return handlerMap.get(apiKey);
    }

}
