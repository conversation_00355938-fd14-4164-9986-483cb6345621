package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.kafka.GroupDescription;
import com.shuyun.fast.kafka.GroupListing;
import com.shuyun.fast.kafka.KafkaSender;
import com.shuyun.fast.kafka.PartitionOffset;
import com.shuyun.fast.util.JsonUtil;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.core.convert.format.Format;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.core.util.StringUtils;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Get;
import io.micronaut.http.annotation.QueryValue;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.*;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.ConsumerGroupState;
import org.apache.kafka.common.TopicPartition;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static com.shuyun.fast.base.ApiTags.API_RESP_CODE_500100;

@Tag(name = "kafka管理")
@ExecuteOn("blocking")
@Controller("/v1/0.0/internal/kafka")
@Slf4j
public class KafkaResource {

    private final AdminClient adminClient;
    private final KafkaSender kafkaSender;

    public KafkaResource(@NonNull AdminClient adminClient,
                         KafkaSender kafkaSender){
        this.adminClient = adminClient;
        this.kafkaSender = kafkaSender;
    }

    @Get("/message/mock")
    public ApiResult<String> messageMock(@QueryValue @NotBlank String topic,
                                         @QueryValue @NotBlank String key,
                                         @QueryValue @NotBlank String message) throws Exception{
        DescribeTopicsResult result = adminClient.describeTopics(Collections.singleton(topic));
        TopicDescription td = result.values().get(topic).get();
        if(StringUtils.isEmpty(td.name())){
            throw new ApiException(ApiTags.API_RESP_CODE_500100, "topic not exist");
        }
        kafkaSender.send(topic, key, message);
        return ApiResult.success("message send success");
    }

    @Get("/topic/list")
    public ApiResult<Set<String>> listTopic() throws Exception{
        ListTopicsResult result = adminClient.listTopics();
        log.info("topic list:{}", result.names().get());
        return ApiResult.success(result.names().get());
    }

    @Get("/topic/describe")
    public ApiResult<Object> describeTopic(@QueryValue @NotBlank String topic) throws Exception{
        DescribeTopicsResult result = adminClient.describeTopics(Collections.singleton(topic));
        TopicDescription td = result.values().get(topic).get();
        Map<String, Object> info = new HashMap<>();
        if(StringUtils.isNotEmpty(td.name())){
            info.put("topic", td.name());
            info.put("partitions", td.partitions().size());
        }
        log.info("topic info:{}", info);
        return ApiResult.success(info);
    }

    @Get("/topic/create")
    public ApiResult<String> createTopic(@QueryValue @NotBlank String topic,
                                         @QueryValue @NotBlank int partitions,
                                         @QueryValue @NotBlank short replications){
        NewTopic t = new NewTopic(topic, partitions, replications);
        CreateTopicsOptions options = new CreateTopicsOptions().timeoutMs(5000).retryOnQuotaViolation(false);
        CreateTopicsResult result = adminClient.createTopics(Collections.singletonList(t), options);
        log.info("create kafka topic:{} success", topic);
        return ApiResult.success("create topic success");
    }

    @Get("/partition/add")
    public ApiResult<String> addPartitions(@QueryValue @NotBlank String topic,
                                           @QueryValue @NotBlank int partitions){
        NewPartitions ps = NewPartitions.increaseTo(partitions);
        CreatePartitionsResult result = adminClient.createPartitions(Map.of(topic, ps));
        log.info("add partition for:{} success", topic);
        return ApiResult.success("add partition success");
    }

    @Get("/groups/list")
    public ApiResult<Set<GroupListing>> listGroups(@QueryValue @Nullable String grepKey) throws Exception{
        ListConsumerGroupsResult result = adminClient.listConsumerGroups();
        Set<GroupListing> groups = new HashSet<>();
        if(CollectionUtils.isNotEmpty(result.valid().get())){
            result.valid().get().forEach(g->{
                GroupListing group = new GroupListing();
                group.setGroupId(g.groupId());
                group.setSimpleConsumerGroup(g.isSimpleConsumerGroup());
                group.setState(g.state());
                if(StringUtils.isEmpty(grepKey)){
                    groups.add(group);
                }else if(group.getGroupId().contains(grepKey)){
                    groups.add(group);
                }else{

                }
            });
        }
        log.info("groups list:{}", JsonUtil.serialize(groups));
        return ApiResult.success(groups);
    }

    @Get("/groups/describe")
    public ApiResult<Map<String, GroupDescription>> describeGroups(@QueryValue( value = "groupIds") @Format("MULTI") @NotNull Collection<String> groupIds) throws Exception{
        DescribeConsumerGroupsResult result = adminClient.describeConsumerGroups(groupIds);
        Map<String, GroupDescription> map = new HashMap<>();
        Map<String, ConsumerGroupDescription> groupsInfo = result.all().get();
        Iterator<Map.Entry<String, ConsumerGroupDescription>> iterator = groupsInfo.entrySet().iterator();
        while(iterator.hasNext()){
            Map.Entry<String, ConsumerGroupDescription> entry = iterator.next();
            ConsumerGroupDescription consumerGroupDescription = entry.getValue();
            GroupDescription groupDescription = new GroupDescription();
            groupDescription.setCoordinator(consumerGroupDescription.coordinator());
            groupDescription.setMembers(consumerGroupDescription.members().stream().map(d->d.consumerId()).collect(Collectors.toSet()));
            groupDescription.setSimpleConsumerGroup(consumerGroupDescription.isSimpleConsumerGroup());
            groupDescription.setState(consumerGroupDescription.state());
            groupDescription.setAuthorizedOperations(consumerGroupDescription.authorizedOperations());
            groupDescription.setGroupId(consumerGroupDescription.groupId());
            groupDescription.setPartitionAssignor(consumerGroupDescription.partitionAssignor());

            ListConsumerGroupOffsetsResult offsetsResult = adminClient.listConsumerGroupOffsets(consumerGroupDescription.groupId());
            Map<TopicPartition, OffsetAndMetadata> currentOffsets = offsetsResult.partitionsToOffsetAndMetadata().get();
            Map<TopicPartition, OffsetSpec> topicPartitionOffsets = new HashMap<>();
            currentOffsets.forEach((k, v)->{
                topicPartitionOffsets.put(k, OffsetSpec.latest());
            });
            Map<TopicPartition, ListOffsetsResult.ListOffsetsResultInfo> listOffsetsResult = adminClient.listOffsets(topicPartitionOffsets).all().get();
            List<PartitionOffset> offsets = new ArrayList<>();
            topicPartitionOffsets.forEach((k,v)->{
                PartitionOffset offset = new PartitionOffset();
                offset.setTopic(k.topic());
                offset.setPartition(k.partition());
                offset.setCurrentOffset(currentOffsets.get(k).offset());
                offset.setLogEndOffset(listOffsetsResult.get(k).offset());
                offset.setLag(offset.getLogEndOffset() - offset.getCurrentOffset());
                offsets.add(offset);
            });
            groupDescription.setOffsets(offsets);
            map.put(entry.getKey(), groupDescription);
        }
        log.info("groups info:{}", JsonUtil.serialize(map));
        return ApiResult.success(map);
    }

    @Get("/groups/delete")
    public ApiResult<Object> deleteGroups(@QueryValue( value = "groupIds") @Format("MULTI") @NotNull Collection<String> groupIds) throws Exception{
        DescribeConsumerGroupsResult groupsResult = adminClient.describeConsumerGroups(groupIds);
        Map<String, GroupDescription> map = new HashMap<>();
        Map<String, ConsumerGroupDescription> groupsInfo = groupsResult.all().get();
        Iterator<Map.Entry<String, ConsumerGroupDescription>> iterator = groupsInfo.entrySet().iterator();
        boolean canDelete = true;
        while(iterator.hasNext()){
            Map.Entry<String, ConsumerGroupDescription> entry = iterator.next();
            ConsumerGroupDescription consumerGroupDescription = entry.getValue();
            GroupDescription groupDescription = new GroupDescription();
            groupDescription.setCoordinator(consumerGroupDescription.coordinator());
            groupDescription.setMembers(consumerGroupDescription.members().stream().map(d->d.consumerId()).collect(Collectors.toSet()));
            groupDescription.setSimpleConsumerGroup(consumerGroupDescription.isSimpleConsumerGroup());
            groupDescription.setState(consumerGroupDescription.state());
            groupDescription.setAuthorizedOperations(consumerGroupDescription.authorizedOperations());
            groupDescription.setGroupId(consumerGroupDescription.groupId());
            groupDescription.setPartitionAssignor(consumerGroupDescription.partitionAssignor());
            map.put(entry.getKey(), groupDescription);
            if(!ConsumerGroupState.EMPTY.equals(groupDescription.getState())){
                canDelete = false;
            }
            if(!CollectionUtils.isEmpty(groupDescription.getMembers())){
                canDelete = false;
            }
        }
        if(canDelete){
            //delete之前务必判断group状态和member属性确保无人使用此group,调用describeGroups进行关键属性判断
            DeleteConsumerGroupsResult result = adminClient.deleteConsumerGroups(groupIds);
            return ApiResult.success("start delete groups");
        }
        return ApiResult.success(map);
    }

}
