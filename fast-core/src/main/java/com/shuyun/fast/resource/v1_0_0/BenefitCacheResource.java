package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.service.v1_0_0.BenefitService;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.lite.client.DiscoveryClientHolder;
import com.shuyun.lite.context.GlobalContext;
import com.shuyun.spectrum.service.spec.Service;
import io.micronaut.context.annotation.Requires;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Get;
import io.micronaut.http.annotation.QueryValue;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Tag(name = "卡券缓存管理")
@ExecuteOn("blocking")
@Controller("/v1/0.0/internal/benefit")
@Slf4j
@Requires(property = "benefit.enable", value = "true", defaultValue = "false")
public class BenefitCacheResource {

    private final BenefitService benefitService;

    public BenefitCacheResource(BenefitService benefitService){
        this.benefitService = benefitService;
    }

    @Get("/selector/get")
    public ApiResult<List<String>> selectorCache(@QueryValue @NotBlank String tenantId,
                                                 @QueryValue @NotBlank String selectorId){
        List<String> data = benefitService.loadSelector(tenantId, selectorId);
        return ApiResult.success(data);
    }

    @Get("/refresh")
    public ApiResult<Void> refresh(@QueryValue @NotBlank String tenantId,
                                   @QueryValue @NotBlank String bizCode,
                                   @QueryValue @NotBlank String projectId){
        benefitService.refreshProject(projectId);
        return ApiResult.success();
    }

    @Get("/refreshAllInstance")
    public ApiResult<Void> refreshAllInstance(@QueryValue @NotBlank String tenantId,
                                              @QueryValue @NotBlank String bizCode,
                                              @QueryValue @NotBlank String projectId) throws Exception{
        String serviceId = "fast-service";
        String env = GlobalContext.environment();
        List<Service> instances = DiscoveryClientHolder.findAll(env, serviceId, "v1");
        for(Service instance:instances){
            String url = "http://ip:port/fast-service/v1/0.0/internal/benefit/refresh?tenantId=@tenantId&bizCode=@bizCode&projectId=@projectId";
            String ip = instance.localIp();
            Integer port = instance.servicePort(8080);
            url = url.replace("ip", ip)
                    .replace("port", String.valueOf(port))
                    .replace("@tenantId", tenantId)
                    .replace("@bizCode", bizCode)
                    .replace("@projectId", projectId);

            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            String respBody = response.body().string();
            log.info("refresh remote benefit project cache resp:{}", respBody);
            ApiResult result = JsonUtil.deserialize(respBody, ApiResult.class);
            log.info("refresh instance :{} result:{}", ip, result.isSuccess());
            //直接用ip端口请求对应实例的/refresh接口刷新缓存
        }

        return ApiResult.success();
    }

}
