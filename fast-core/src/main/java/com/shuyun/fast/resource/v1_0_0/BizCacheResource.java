package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.service.v1_0_0.BizCacheService;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.lite.client.DiscoveryClientHolder;
import com.shuyun.lite.context.GlobalContext;
import com.shuyun.spectrum.service.spec.Service;
import io.micronaut.context.annotation.Value;
import io.micronaut.scheduling.annotation.ExecuteOn;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import io.micronaut.http.annotation.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import okhttp3.Request;
import okhttp3.Response;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Tag(name = "服务缓存管理")
@ExecuteOn("blocking")
@Controller("/v1/0.0/internal/bizCache")
@Slf4j
public class BizCacheResource {

    private final BizCacheService bizCacheService;

    public BizCacheResource(BizCacheService bizCacheService){
        this.bizCacheService = bizCacheService;
    }
    @Value("${micronaut.application.name}")
    private String serviceId;

    @Get("/get")
    public ApiResult<Object> get(@QueryValue @NotBlank String tenantId,
                                 @QueryValue @NotBlank String bizCode,
                                 @QueryValue @NotBlank String cacheType){
        return ApiResult.success(bizCacheService.get(tenantId, bizCode, cacheType));
    }

    @Get("/refresh")
    public ApiResult<Void> refresh(@QueryValue @NotBlank String tenantId,
                                   @QueryValue @NotBlank String bizCode,
                                   @QueryValue @NotBlank String cacheType){
        bizCacheService.refresh(tenantId, bizCode, cacheType);
        return ApiResult.success();
    }

    @Get("/refreshAllInstance")
    public ApiResult<Void> refreshAllInstance(@QueryValue @NotBlank String tenantId,
                                              @QueryValue @NotBlank String bizCode,
                                              @QueryValue @NotBlank String cacheType) throws Exception{
        String env = GlobalContext.environment();
        List<Service> instances = DiscoveryClientHolder.findAll(env, serviceId, "v1");
        for(Service instance:instances){
            String url = "http://ip:port/serviceId/v1/0.0/internal/bizCache/refresh?tenantId=@tenantId&bizCode=@bizCode&cacheType=@cacheType";
            String ip = instance.localIp();
            Integer port = instance.servicePort(8080);
            url = url.replace("ip", ip)
                    .replace("port", String.valueOf(port))
                    .replace("serviceId", serviceId)
                    .replace("@tenantId", tenantId)
                    .replace("@bizCode", bizCode)
                    .replace("@cacheType", cacheType);

            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .addHeader("Content-Type", "application/json")
                    .build();
            //直接用ip端口请求对应实例的/refresh接口刷新缓存
            Response response = client.newCall(request).execute();
            String respBody = response.body().string();
            log.info("remote refresh invoke resp:{}", respBody);
            ApiResult result = JsonUtil.deserialize(respBody, ApiResult.class);
            log.info("refresh instance :{} result:{}", ip, result.isSuccess());
        }
        return ApiResult.success();
    }

}
