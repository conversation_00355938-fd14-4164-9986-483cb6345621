package com.shuyun.fast.util;

import com.shuyun.dm.api.response.DMLResponse;
import com.shuyun.dm.dataapi.sdk.DataapiSdkFactory;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.dm.sdk.Options;
import com.shuyun.spectrum.client.common.SysEnvUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Slf4j
public class DataapiSdkUtil {
    public static DataapiHttpSdk getDataapiHttpSdk(){
        return dataapiHttpSdk;
    }

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkFactory.INSTANCE.createDataapiHttpSdk();

    static final String dataApiService = SysEnvUtils.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.targetServer");

    public static DMLResponse upsertIsData(String fqnName, String id, Map<String, Object> paramsMap) {
        DMLResponse response = new DMLResponse();
        final DataapiHttpSdk dataapiHttpSdk;
        try {
            if (StringUtils.isEmpty(dataApiService)) {
                dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();
            } else {
                Options options = Options.Companion.newBuilder().enableSign(true)
                        .caller(SysEnvUtils.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.name"))
                        .secret(SysEnvUtils.getSysOrEnv("dataapi/v1.ribbon.com.shuyun.motor.client.sign.secret"))
                        .version("v1")
                        .build();
                dataapiHttpSdk = DataapiSdkFactory.INSTANCE.createDataapiHttpSdk(dataApiService, options, "customized-service");
            }
            response = dataapiHttpSdk.upsert(fqnName, id, paramsMap, false);
        } catch (Exception ex) {
            //log.error("dataapiHttpSdk.upsert ====> upsert database error !,msg:{}", ex);
            response.setIsSuccess(false);
            response.setOperation(ex.getMessage());
        }
        log.info("数据服务访问" + fqnName + "返回影响行数:{}", response.getAffectedRows());
        return response;
    }
}
