package com.shuyun.fast.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.shuyun.fast.ls.result.SyncMemberResponse;
import com.shuyun.lite.util.Common;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

@Slf4j
public final class ExternalApiClient {

    private static final ObjectMapper MAPPER = new ObjectMapper();
    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();

    /**
     * 统一调用入口
     *
     * @param bizParams     业务参数（不含 organizationId / timestamp）
     * @return SyncMemberResponse
     */
    public static SyncMemberResponse lsSyncCall(Map<String, String> bizParams) throws IOException {
        String url = Common.getSysOrEnv("ls.member.sync.url","https://srslawson-dev.yorentown.com/market/security/pass/external/SYNC_MEMBER");
        String organizationId = Common.getSysOrEnv("ls.organizationId.key","kele");
        log.info("lsSyncCall...接口url:{}",url);
        log.info("lsSyncCall...接口organizationId:{}",organizationId);
        // 1. 构造全部待签参数
        TreeMap<String, String> signMap = new TreeMap<>();
        signMap.put("organizationId", organizationId);
        signMap.putAll(bizParams);
        String ts = String.valueOf(System.currentTimeMillis());
        signMap.put("timestamp", ts);
        log.info("lsSyncCall...接口signStr:{}",JsonUtil.outPutSerialize(signMap));

        // 2. 计算签名
        String signStr = buildSignString(signMap);
        log.info("lsSyncCall...接口signStr:{}",signStr);
        String signature = sha256(signStr);
        log.info("lsSyncCall...接口signature:{}",signature);

        // 3. 请求体（仅业务参数，不含 organizationId/timestamp）
        RequestBody body = RequestBody.create(
                MAPPER.writeValueAsString(bizParams),
                MediaType.parse("application/json; charset=utf-8")
        );

        log.info("lsSyncCall...接口body:{}",JsonUtil.outPutSerialize(body));
        Request request = new Request.Builder()
                .url(url)
                .header("timestamp", ts)
                .header("signature", signature)
                .post(body)
                .build();
        log.info("接口respBody:{}",JsonUtil.outPutSerialize(request));
        try (Response response = CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("HTTP " + response.code());
            }
            String respBody = response.body() == null ? "" : response.body().string();
            log.info("接口respBody:{}",respBody);
            return MAPPER.readValue(respBody, SyncMemberResponse.class);
        }
    }

    /* ---------- 工具 ---------- */

    /** 生成待签串：key=value&key=value（已排序） */
    private static String buildSignString(TreeMap<String, String> map) {
        StringBuilder sb = new StringBuilder();
        map.forEach((k, v) -> {
            if (sb.length() > 0) sb.append('&');
            sb.append(k).append('=').append(v);
        });
        return sb.toString();
    }

    /** SHA-256（小写 64 位） */
    private static String sha256(String content) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(content.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) sb.append(String.format("%02x", b));
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /* ---------- 测试 ---------- */
    public static void main(String[] args) throws Exception {
        Map<String, String> biz = Map.of(
                "userId", "41306692",
                "orgUserId", "9e6917e3be7b4d62999ad6d19ecb6ab4",
                "organizationId", "kele"
        );

        SyncMemberResponse resp = lsSyncCall(biz);
        log.info("接收结果：code={}, message={}", resp.getCode(), resp.getMessage());
    }
}