package com.shuyun.fast.util;
import com.shuyun.dm.api.vo.FetchStartRequest;
import com.shuyun.dm.api.vo.FetchStartResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import io.micronaut.core.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

@Slf4j
public class StreamQuery {
    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    public static void query(String sql, Map<String, Object> params, int fetchSize, Long totalSizeLimit, Function<List<Map<String,Object>>,Void> consumer) {
        if (params == null)
            params = new HashMap<>();
        if(Objects.isNull(totalSizeLimit)){
            totalSizeLimit = Long.MAX_VALUE;
        }
        FetchStartRequest request = new FetchStartRequest(sql.toString(), params, fetchSize);
        try (FetchStartResponse response = dataapiHttpSdk.fetch(request)) {
            List<Map<String,Object>> data;
            long totalSize = 0;
            do {
                data = response.next().getData();
                if (!CollectionUtils.isEmpty(data)) {
                    totalSize += data.size();
                    if(totalSize > totalSizeLimit){
                        log.info("sql:{} data size reach limited size:{}", sql, totalSizeLimit);
                        return ;
                    }
                    consumer.apply(data);
                }
            } while (data != null && data.size() > 0);
        } catch (Exception e) {
            log.error("stream query data error:", e);
        }
    }
}
