package com.shuyun.fast.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.FilterProvider;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.collect.Lists;
import com.shuyun.fast.ser.OutPutLocalDateTimeSerializer;
import com.shuyun.fast.ser.UtcTimeToLocalDateTimeDeserializer;
import com.shuyun.fast.ser.OutputZonedDateTimeSerializer;
import com.shuyun.fast.ser.UtcTimeToLocalTimeDeserializer;
import com.shuyun.fast.v1_0_0.domain.CouponProject;
import com.shuyun.fast.v1_0_0.param.member.MemberBindParam;
import com.shuyun.fast.v1_0_0.result.MemberGetResult;
import com.shuyun.kylin.member.api.request.BindRequest;
import com.shuyun.ticket.benefit.domain.BenefitProject;
import lombok.extern.slf4j.Slf4j;
import com.shuyun.kylin.member.api.response.MemberQueryData;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
public class JsonUtil extends com.shuyun.ticket.util.JsonUtil {

    private final static ObjectMapper outPutObjectMapper = new ObjectMapper();

    static{
        SimpleModule module = new SimpleModule();
        module.addSerializer(LocalDateTime.class, new OutPutLocalDateTimeSerializer());//产品接口LocalDateTime出参序列化
        module.addSerializer(ZonedDateTime.class, new OutputZonedDateTimeSerializer());//产品接口出参时间序列化
        module.addDeserializer(LocalDateTime.class, new UtcTimeToLocalDateTimeDeserializer());//产品接口出参反序列化
        module.addDeserializer(LocalTime.class, new UtcTimeToLocalTimeDeserializer());//店铺开关店时间转换

        outPutObjectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        outPutObjectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        outPutObjectMapper.registerModule(new JavaTimeModule());
        outPutObjectMapper.registerModule(module);

        getObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public static <T> T convert(Object fromValue, Class<T> toValueType) {

        return fromValue == null ? null : deserialize(serialize(fromValue), toValueType);
    }

    public static <T> List<T> outPutListFromJson(String jsonStr, Class<T> elementClazz) {
        if (jsonStr == null) {
            return Lists.newArrayList();
        } else {
            String s = jsonStr.trim();
            if (s.isEmpty()) {
                return Lists.newArrayList();
            } else {
                ObjectMapper m = outPutObjectMapper;
                CollectionType collectionType = m.getTypeFactory().constructCollectionType(List.class, elementClazz);

                try {
                    return (List)m.readValue(s, collectionType);
                } catch (JsonProcessingException var6) {
                    JsonProcessingException e = var6;
                    throw new IllegalArgumentException(e);
                }
            }
        }
    }

    public static <T> T outPutConvert(Object fromValue, Class<T> toValueType) {
        return fromValue == null ? null : outPutDeserialize(outPutSerialize(fromValue), toValueType);
    }

    public static String outPutSerialize(Object o) {
        try {
            return outPutObjectMapper.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException(e);
        }
    }


    public static <T> T outPutDeserialize(String s, Class<T> c) {
        try {
            return outPutObjectMapper.readValue(s, c);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException(e);
        }
    }


    public static <T> T filterConvert(Object fromValue, Class<T> toValueType, String jsonFilter, String... ignoreFields) {
        if(fromValue == null){
            return null;
        }
        SimpleBeanPropertyFilter filter = SimpleBeanPropertyFilter.serializeAllExcept(ignoreFields);
        SimpleFilterProvider filterProvider = new SimpleFilterProvider();
        if(jsonFilter.contains(",")){
            String[] jsonFilters = jsonFilter.split(",");
            for (String jsonFilterItem : jsonFilters) {
                filterProvider.addFilter(jsonFilterItem, filter);
            }
        }else{
            filterProvider.addFilter(jsonFilter, filter);
        }
        try {
            String json = getObjectMapper().writer(filterProvider).writeValueAsString(fromValue);
            log.debug("json.........{}", json);
            return deserialize(json, toValueType);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException(e);
        }
    }


    public static void main(String[] args){

//        BenefitResponse benefit = new BenefitResponse();
//        benefit.setGrantAt(LocalDateTime.now());
//        CouponGetResult cr = JsonUtil.outPutConvert(benefit, CouponGetResult.class);
//
//        MemberBindParam param = new MemberBindParam();
//        param.setBindTime(LocalDateTime.now());
//        BindRequest request = JsonUtil.outPutConvert(param, BindRequest.class);
//        log.info("request:{}", JsonUtil.serialize(request));

        BenefitProject project = new BenefitProject();
        project.setUpdateAt(LocalDateTime.now().plusHours(-8));
        CouponProject cp = JsonUtil.outPutConvert(project, CouponProject.class);


        DateTimeFormatter utcFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
//        String utcTimeString =  utcFormatter.format(DateUtil.utcTime(LocalDateTime.now()));
//        ZonedDateTime utcTime = ZonedDateTime.parse(utcTimeString, utcFormatter);
//        ZonedDateTime time = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
//        LocalDateTime localTime = time.toLocalDateTime();

        MemberQueryData data =  new MemberQueryData();

        data.setEnrollTime(utcFormatter.format(DateUtil.utcTime(LocalDateTime.now())));
        MemberGetResult r = JsonUtil.outPutConvert(data, MemberGetResult.class);
        r.setRegisterTime(DateUtil.localTime(data.getEnrollTime()));
//        CouponGetResult cr = JsonUtil.filterConvert(benefit, CouponGetResult.class,
//                "couponGetResultFilter",
//                "grantAt", "effectiveAt", "useAt", "expiredAt", "activateAt");
        log.info("cr:{}", JsonUtil.serialize(r));

        MemberBindParam bindParam = new MemberBindParam();
        bindParam.setBindTime(LocalDateTime.now());
        BindRequest request = JsonUtil.convert(bindParam, BindRequest.class);

        log.info("bind request:{}", JsonUtil.serialize(request));
    }
}
