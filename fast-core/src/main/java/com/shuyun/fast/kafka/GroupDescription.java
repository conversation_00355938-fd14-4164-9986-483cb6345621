package com.shuyun.fast.kafka;

import lombok.Data;
import org.apache.kafka.common.ConsumerGroupState;
import org.apache.kafka.common.Node;
import org.apache.kafka.common.acl.AclOperation;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * field from {@link org.apache.kafka.clients.admin.ConsumerGroupDescription}
 */
@Data
public class GroupDescription {
    private String groupId;
    private boolean isSimpleConsumerGroup;
    private Collection<String> members;
    private String partitionAssignor;
    private ConsumerGroupState state;
    private Node coordinator;
    private Set<AclOperation> authorizedOperations;
    private List<PartitionOffset> offsets;
}
