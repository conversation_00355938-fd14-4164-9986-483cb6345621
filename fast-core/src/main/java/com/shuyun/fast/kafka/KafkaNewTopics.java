package com.shuyun.fast.kafka;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Requires;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.CreateTopicsOptions;
import org.apache.kafka.clients.admin.CreateTopicsResult;
import org.apache.kafka.clients.admin.NewTopic;
import java.util.List;

@Context
@Requires(bean = AdminClient.class)
@Requires(bean = NewTopic.class)
@Slf4j
public class KafkaNewTopics {
    @NonNull
    private final CreateTopicsResult result;

    /**
     * @param adminClient The Kafka admin client.
     * @param options Optional {@link CreateTopicsOptions}.
     * @param topics  The list of {@link NewTopic} beans to create.
     */
    public KafkaNewTopics(
            @NonNull AdminClient adminClient,
            @Nullable CreateTopicsOptions options,
            @NonNull List<NewTopic> topics) {
        this.result = adminClient.createTopics(topics, options != null ? options : new CreateTopicsOptions());
        this.result.values()
                .entrySet()
                .stream()
                .forEach(f->{
                    try {
                        f.getValue().get();
                    } catch (Exception e){
//                        log.error("error:", e);
                    }
                });
        log.info("creating new topics: {} finished", topics);
    }
}
