package com.shuyun.fast.exception;

import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import io.micronaut.context.LocalizedMessageSource;
import io.micronaut.context.annotation.Replaces;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.annotation.Produces;
import io.micronaut.http.server.exceptions.ExceptionHandler;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolationException;

@Slf4j
@Produces
@Singleton
@Replaces(io.micronaut.validation.exceptions.ConstraintExceptionHandler.class)
public class ConstraintExceptionHandler implements ExceptionHandler<ConstraintViolationException, ApiResult<Void>> {

    private LocalizedMessageSource messageSource;

    public ConstraintExceptionHandler(LocalizedMessageSource messageSource){
        this.messageSource = messageSource;
    }



    @Override
    public ApiResult<Void> handle(HttpRequest request, ConstraintViolationException exception) {
        String code = String.format("%6d", ApiTags.API_RESP_CODE_100001);
        String msg = messageSource.getMessageOrDefault(code, "", exception.getLocalizedMessage());
        log.error(msg);
        return ApiResult.failure(code, msg);

    }
}
