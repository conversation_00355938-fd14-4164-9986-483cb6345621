package com.shuyun.fast.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ApiException extends RuntimeException{

    private Integer code;
    private Object[] variables = {};

    public ApiException(){
        super();
    }
    public ApiException(String message){
        super(message);
    }
    public ApiException(Integer code){
        this("");
        this.code = code;
    }

    public ApiException(Integer code, Object... variables){
        this(code);
        this.variables = variables;
    }
}
