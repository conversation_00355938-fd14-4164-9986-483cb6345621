package com.shuyun.fast.exception;

import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.model.MbspErrorResult;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.kylin.member.api.response.MemberBaseResult;
import io.micronaut.context.LocalizedMessageSource;
import io.micronaut.context.annotation.Requires;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.annotation.Produces;
import io.micronaut.http.client.exceptions.HttpClientResponseException;
import io.micronaut.http.server.exceptions.ExceptionHandler;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolationException;
import java.util.Optional;

@Slf4j
@Produces
@Singleton
@Requires(classes = {RuntimeException.class, ExceptionHandler.class})
public class ApiExceptionHandler implements ExceptionHandler<RuntimeException, ApiResult<Void>> {

    private LocalizedMessageSource messageSource;

    public ApiExceptionHandler(LocalizedMessageSource messageSource){
        this.messageSource = messageSource;
    }



    @Override
    public ApiResult<Void> handle(HttpRequest request, RuntimeException exception) {
        log.error("exception:", exception);
        if(exception instanceof ApiException){
            ApiException apiException = (ApiException)exception;
            String code = String.format("%6d", apiException.getCode());
            String msg = messageSource.getMessageOrDefault(code, "", apiException.getVariables());
            return ApiResult.failure(code, msg);
        }

        if(exception instanceof HttpClientResponseException){
            HttpClientResponseException cre = (HttpClientResponseException)exception;
            MbspErrorResult errBody = cre.getResponse().getBody(MbspErrorResult.class).orElse(null);
            log.error("http client response exception body:{}", JsonUtil.serialize(errBody));
            String code = String.format("%6d", ApiTags.API_RESP_CODE_500100);
            String msg = errBody.getMsg();
            return ApiResult.failure(code, msg);
        }

        String code = String.format("%6d", ApiTags.API_RESP_CODE_500100);
        String msg = messageSource.getMessageOrDefault(code, "", exception.getLocalizedMessage());
        return ApiResult.failure(code, msg);
    }
}
