package com.shuyun.fast.factory;

import com.shuyun.fast.config.RedissonConfiguration;
import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Requires;
import io.micronaut.core.util.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;

import java.util.Arrays;
import java.util.stream.Collectors;

@Factory
public class RedissonFactory {

    private RedissonConfiguration configuration;

    public RedissonFactory(RedissonConfiguration configuration) {
        this.configuration = configuration;
    }

    @Bean
    @Requires(property = "fast.redis.enable", value = "true", defaultValue = "false")
    public JsonJacksonCodec jsonJacksonCodec() {
        return new JsonJacksonCodec();
    }

    @Bean
    @Requires(property = "fast.redis.enable", value = "true", defaultValue = "false")
    public RedissonClient getRedission(JsonJacksonCodec jsonJacksonCodec) {
        boolean isSsl = Boolean.parseBoolean(configuration.getSsl());
        String address = (String) Arrays.stream(this.configuration.getAddress().split(",")).map((add) -> {
            if (!add.startsWith("redis://")) {
                add = "redis://" + add;
            }

            if (isSsl) {
                add = add.replace("redis:", "rediss:");
            }

            return add;
        }).collect(Collectors.joining(","));
        return !StringUtils.isEmpty(address) && address.indexOf(",") > 0 ? this.getMultiRedis(address.split(","), jsonJacksonCodec) : this.getSingleRedis(address, jsonJacksonCodec);
    }

    private RedissonClient getSingleRedis(String address, JsonJacksonCodec jsonJacksonCodec) {
        Config config = new Config();
        config.setCodec(jsonJacksonCodec);
        SingleServerConfig singleServerConfig = config.useSingleServer();
        int poolSize = Integer.parseInt(this.configuration.getPoolSize());
        if (poolSize < 64) {
            poolSize = 64;
        }

        singleServerConfig.setAddress(address).setDatabase(Integer.parseInt(this.configuration.getDatabase())).setConnectionPoolSize(poolSize);
        if (!StringUtils.isEmpty(this.configuration.getPassword())) {
            singleServerConfig.setPassword(this.configuration.getPassword());
        }

        return Redisson.create(config);
    }

    private RedissonClient getMultiRedis(String[] addresses, JsonJacksonCodec jsonJacksonCodec) {
        Config config = new Config();
        config.setNettyThreads(64);
        config.setCodec(jsonJacksonCodec);
        ClusterServersConfig clusterServersConfig = config.useClusterServers();
        clusterServersConfig.setScanInterval(2000).addNodeAddress(addresses).setMasterConnectionPoolSize(Integer.parseInt(this.configuration.getPoolSize()));
        if (!StringUtils.isEmpty(this.configuration.getPassword())) {
            clusterServersConfig.setPassword(this.configuration.getPassword());
        }

        return Redisson.create(config);
    }

}
