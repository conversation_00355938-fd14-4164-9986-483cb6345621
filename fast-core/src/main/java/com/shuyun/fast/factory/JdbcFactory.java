package com.shuyun.fast.factory;

import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Factory;
import io.micronaut.transaction.jdbc.DelegatingDataSource;
import jakarta.inject.Named;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

@Factory
public class JdbcFactory {
    
    @Bean
    public JdbcTemplate jdbcTemplate(@Named("default") DataSource dataSource){
        return new JdbcTemplate(DelegatingDataSource.unwrapDataSource(dataSource));
    }
}
