package com.shuyun.fast.factory;
import cn.hutool.core.thread.NamedThreadFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: xiaolong.chang
 * @Date: 2018/10/19
 * @Description:
 */
@Slf4j
public class ThreadPoolExecutorFactory {

    public static ThreadPoolExecutor get(){
        return new ThreadPoolExecutor(6,
                12,
                0,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(),
                new IgnorePolicy());
    }


    public static ThreadPoolExecutor get(Integer queueSize){
        return new ThreadPoolExecutor(6,
                12,
                0,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(queueSize),
                new IgnorePolicy());
    }

    public static ThreadPoolExecutor get(Integer coreSize,
                                         Integer maxSize,
                                         Integer queueSize,
                                         String threadName){
        return new ThreadPoolExecutor(coreSize,
                maxSize,
                0,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(queueSize),
                new NamedThreadFactory(threadName, false),
                new IgnorePolicy());
    }

    public static class IgnorePolicy implements RejectedExecutionHandler{

        public IgnorePolicy(){

        }

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            log.info("达到线程池处理能力上限, 忽略此任务");
        }
    }

}
