package com.shuyun.fast.annotation;

import com.shuyun.fast.base.ApiTags;
import io.micronaut.aop.Around;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Documented
@Retention(RUNTIME) // (1)
@Target({TYPE, METHOD}) // (2)
@Around
public @interface Api {
    String version() default ApiTags.API_VERSION_1_0_0;
    String name() default "";
}
