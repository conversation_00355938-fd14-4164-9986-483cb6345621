package com.shuyun.fast.cache.v1_0_0;

import com.shuyun.fast.annotation.BizCacheType;
import com.shuyun.fast.entity.BizCache;
import io.micronaut.core.annotation.Introspected;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
@BizCacheType("benefit")
public class BenefitCache extends BaseCache{
    private String programId;
    private String subjectFqn;
    //选择器数据owner对象
    private String selectorOwner;
}
