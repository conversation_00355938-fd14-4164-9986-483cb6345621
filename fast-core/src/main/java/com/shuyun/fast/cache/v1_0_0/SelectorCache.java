package com.shuyun.fast.cache.v1_0_0;


import com.shuyun.fast.annotation.BizCacheType;
import io.micronaut.core.annotation.Introspected;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
@BizCacheType("selector")
public class SelectorCache extends BaseCache{
    private String programId;
    private List<String> names;
}
