package com.shuyun.fast.cache.v1_0_0;

import com.shuyun.fast.annotation.BizCacheType;
import io.micronaut.core.annotation.Introspected;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
@BizCacheType("point")
public class PointCache extends BaseCache{
    private Long planId;
    private Long pointAccountTypeId;
    private String pointBizType;
}
