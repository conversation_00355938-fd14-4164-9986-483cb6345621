package com.shuyun.fast.cache.v1_0_0;

import com.shuyun.fast.annotation.BizCacheType;
import io.micronaut.core.annotation.Introspected;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
@BizCacheType("tag")
public class TagCache extends BaseCache{
    private String domainCode;
    //非常规理解的接口请求渠道如WECHAT POS等,是cdp特殊的一种业务规则定义,默认MEMBER
    private String channelType = "MEMBER";
}
