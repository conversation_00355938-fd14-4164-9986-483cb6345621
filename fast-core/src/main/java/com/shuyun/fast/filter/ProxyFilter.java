package com.shuyun.fast.filter;

import com.shuyun.fast.router.ApiRouter;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.MutableHttpResponse;
import io.micronaut.http.annotation.Filter;
import io.micronaut.http.client.ProxyHttpClient;
import io.micronaut.http.filter.HttpServerFilter;
import io.micronaut.http.filter.ServerFilterChain;
import io.micronaut.http.uri.UriBuilder;
import io.micronaut.runtime.server.EmbeddedServer;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;

import java.net.URI;

@Filter("/**/kylin.crm.*")
@Slf4j
public class ProxyFilter implements HttpServerFilter {

    private final ProxyHttpClient client;
    private final EmbeddedServer embeddedServer;
    private final ApiRouter apiRouter;

    public ProxyFilter(ProxyHttpClient client,
                       EmbeddedServer embeddedServer,
                       ApiRouter apiRouter) {
        this.client = client;
        this.embeddedServer = embeddedServer;
        this.apiRouter = apiRouter;
    }

    @Override
    public Publisher<MutableHttpResponse<?>> doFilter(HttpRequest<?> request,
                                                      ServerFilterChain chain) {

        log.info("do filter....before request,path:{}", request.getPath());
        UriBuilder builder = UriBuilder.of(request.getUri());
        URI target = builder
                .scheme("http")
                .host(embeddedServer.getHost())
                .port(embeddedServer.getPort())
                .replacePath(apiRouter.route(request.getPath())).build();
        return client.proxy(request.mutate().uri(target));
//        return Publishers.map(client.proxy(request.mutate().uri(target)), response->response);
    }
}
