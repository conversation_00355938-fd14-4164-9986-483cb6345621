package com.shuyun.fast.filter;

import com.shuyun.lite.client.Passport;
import com.shuyun.lite.client.PassportClientFactory;
import com.shuyun.lite.client.Passport.TokenRequest;
import com.shuyun.lite.context.GlobalContext;
import com.shuyun.lite.context.ThreadLocalUserContext;
import com.shuyun.lite.context.UserContext;
import com.shuyun.lite.util.Common;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.MutableHttpRequest;
import io.micronaut.http.annotation.Filter;
import io.micronaut.http.filter.ClientFilterChain;
import io.micronaut.http.filter.HttpClientFilter;
import org.apache.commons.lang3.StringUtils;
import org.reactivestreams.Publisher;

@Filter(
        patterns = {"/benefit-mgmt/**",
        "/benefit-service/**",
        "/data-selector/**",
        "/ebrand-member/**",
        "/epassport/**",
        "/loyalty-facade/**",
        "/loyalty-manager/**",
        "/mbsp-api/**",
        "/cdp-mgmt/**",
        "/prepaid-card-service/**"}
)
public class AddPassportTokenFilter implements HttpClientFilter {
    private final Passport.TokenRequest tokenRequest;

    public AddPassportTokenFilter() {
        String key = Common.getSysOrEnv("system.epassport.appKey", Common.getSysOrEnv("SERVICE_NAME", ""));
        String secret = Common.getSysOrEnv("system.epassport.appSecret", Common.getSysOrEnv("system.api.secret", ""));
        String type = "app";
        String tenantId = GlobalContext.defTenantId();
        this.tokenRequest = TokenRequest.of(key, secret, type, tenantId);
    }

    public Publisher<? extends HttpResponse<?>> doFilter(MutableHttpRequest<?> request, ClientFilterChain chain) {
        if (null == request.getHeaders().get("Authorization")) {
            UserContext uc = ThreadLocalUserContext.currentUserContext();
            String token = null != uc && StringUtils.isNotEmpty(uc.getToken()) ? uc.getToken() : PassportClientFactory.instance().token(this.tokenRequest);
            if (null != token && token.startsWith("Bearer ")) {
                token = token.substring("Bearer ".length());
            }

            if (StringUtils.isNotEmpty(token)) {
                return chain.proceed(request.bearerAuth(token));
            }
        }

        return chain.proceed(request);
    }
}
