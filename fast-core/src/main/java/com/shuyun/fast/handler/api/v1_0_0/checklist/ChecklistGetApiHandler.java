package com.shuyun.fast.handler.api.v1_0_0.checklist;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.ChecklistService;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.domain.MemberId;
import com.shuyun.fast.v1_0_0.param.checklist.ChecklistGetParam;
import com.shuyun.fast.v1_0_0.param.checklist.RiskControlChecklistGetResponse;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.validator.FastValidator;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Singleton
public class ChecklistGetApiHandler extends AbstractApiHandler<ChecklistGetParam, MemberId, ChecklistGetParam, RiskControlChecklistGetResponse> {

    private final ChecklistService checklistService;
    private final MemberService memberService;
    public ChecklistGetApiHandler(ChecklistService checklistService,
                                  MemberService memberService) {
        this.checklistService = checklistService;
        this.memberService = memberService;
    }

    @Override
    public void validate(ChecklistGetParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        if(Objects.nonNull(identify)){
            String memberId = identify.getMemberId();
            String userId = identify.getUserId();
            FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
        }
    }
    @Override
    public ChecklistGetParam prepareParam(ChecklistGetParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public RiskControlChecklistGetResponse request(ChecklistGetParam invokeParam) {
        return checklistService.get(invokeParam);
    }

    @Override
    public MemberId prepareResult(ChecklistGetParam param, RiskControlChecklistGetResponse result) {
        MemberId memberId = new MemberId();
        if(Objects.nonNull(result)){
            memberId.setMemberId(result.getCustomerno());
        }
        return memberId;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_CHECKLIST_GET;
    }
}
