package com.shuyun.fast.handler.api.v1_0_0.member;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.domain.MemberId;
import com.shuyun.fast.v1_0_0.param.member.MemberDynamicCodeIdentifyParam;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class MemberDynamicCodeIdentifyApiHandler extends AbstractApiHandler<MemberDynamicCodeIdentifyParam, MemberId, MemberDynamicCodeIdentifyParam, MemberId> {


    private final MemberService memberService;

    public MemberDynamicCodeIdentifyApiHandler(MemberService memberService) {
        this.memberService = memberService;
    }

    @Override
    public void validate(MemberDynamicCodeIdentifyParam param) {
        super.validate(param);
    }

    @Override
    public MemberDynamicCodeIdentifyParam beforeRequest(MemberDynamicCodeIdentifyParam param) {
        super.beforeRequest(param);
        return param;
    }

    @Override
    public MemberDynamicCodeIdentifyParam prepareParam(MemberDynamicCodeIdentifyParam param) {
        return param;
    }

    @Override
    public MemberId request(MemberDynamicCodeIdentifyParam invokeParam) {
        return memberService.dynamicCodeIdentify(invokeParam);
    }

    @Override
    public MemberId prepareResult(MemberDynamicCodeIdentifyParam param, MemberId result) {
        if(StringUtils.isNotEmpty(result.getMemberId())){
            return result;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500208, "invalid dynamic code");
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_DYNAMICCODE_IDENTIFY;
    }
}
