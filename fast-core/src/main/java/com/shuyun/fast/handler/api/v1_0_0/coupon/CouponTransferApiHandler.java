package com.shuyun.fast.handler.api.v1_0_0.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.BenefitService;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.param.coupon.CouponTransferParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.ticket.base.resource.dto.TicketIdentity;
import com.shuyun.ticket.benefit.vo.request.benefit.BenefitTransferRequest;
import com.shuyun.ticket.benefit.vo.response.benefit.BenefitTransferResponse;
import com.shuyun.ticket.enums.TransactionStatus;
import io.micronaut.context.annotation.Requires;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Singleton
@Requires(property = "benefit.enable", value = "true", defaultValue = "false")
public class CouponTransferApiHandler extends AbstractApiHandler<CouponTransferParam, Void, BenefitTransferRequest, BenefitTransferResponse> {

    private final BenefitService benefitService;
    private final MemberService memberService;
    public CouponTransferApiHandler(MemberService memberService,
                                    BenefitService benefitService){
        this.memberService = memberService;
        this.benefitService = benefitService;
    }

    @Override
    public void validate(CouponTransferParam param) {
        super.validate(param);
        MemberIdentifyParam holder = param.getHolder();
        String memberId = holder.getMemberId();
        String userId = holder.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public CouponTransferParam beforeRequest(CouponTransferParam param) {
        super.beforeRequest(param);
        param.setHolder(memberService.memberIdentify(param.getHolder(), param));
        if(Objects.nonNull(param.getReceiver())){
            param.setReceiver(memberService.memberIdentify(param.getReceiver(), param));
        }
        return param;
    }


    @Override
    public BenefitTransferRequest prepareParam(CouponTransferParam param) {
        BenefitTransferRequest request = benefitService.setSceneParam(new BenefitTransferRequest(), param);
        request.setUser(param.getHolder().getMemberId());
        if(Objects.nonNull(param.getReceiver())){
            request.setReceiver(param.getReceiver().getMemberId());
        }
        request.setIds(param.getCoupons()
                .stream()
                .map(c->new TicketIdentity(c.getId(), c.getCode(), c.getProjectId()))
                .collect(Collectors.toList()));
        return request;
    }

    @Override
    public BenefitTransferResponse request(BenefitTransferRequest invokeParam) {
        return benefitService.transfer(invokeParam);
    }

    @Override
    public Void prepareResult(CouponTransferParam param, BenefitTransferResponse result) {
        if(TransactionStatus.SUCCESS.getCode().equals(result.getStatus().getCode())){
            return null;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500239, result.getMessage());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_TRANSFER;
    }
}
