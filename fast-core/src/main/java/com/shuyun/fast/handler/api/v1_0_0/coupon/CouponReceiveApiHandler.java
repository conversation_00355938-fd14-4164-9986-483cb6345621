package com.shuyun.fast.handler.api.v1_0_0.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.BenefitService;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.param.coupon.CouponReceiveParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.ticket.base.resource.dto.TicketIdentity;
import com.shuyun.ticket.benefit.vo.request.benefit.BenefitReceiveRequest;
import com.shuyun.ticket.benefit.vo.response.benefit.BenefitReceiveResponse;
import com.shuyun.ticket.enums.TransactionStatus;
import io.micronaut.context.annotation.Requires;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Singleton
@Requires(property = "benefit.enable", value = "true", defaultValue = "false")
public class CouponReceiveApiHandler extends AbstractApiHandler<CouponReceiveParam, Void, BenefitReceiveRequest, BenefitReceiveResponse> {

    private final BenefitService benefitService;
    private final MemberService memberService;
    public CouponReceiveApiHandler(MemberService memberService,
                                   BenefitService benefitService){
        this.memberService = memberService;
        this.benefitService = benefitService;
    }


    @Override
    public void validate(CouponReceiveParam param) {
        super.validate(param);
        MemberIdentifyParam receiver = param.getReceiver();
        String memberId = receiver.getMemberId();
        String userId = receiver.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public CouponReceiveParam beforeRequest(CouponReceiveParam param) {
        super.beforeRequest(param);
        param.setReceiver(memberService.memberIdentify(param.getReceiver(), param));
        if(Objects.nonNull(param.getHolder())){
            param.setHolder(memberService.memberIdentify(param.getHolder(), param));
        }
        return param;
    }

    @Override
    public BenefitReceiveRequest prepareParam(CouponReceiveParam param) {
        BenefitReceiveRequest request = benefitService.setSceneParam(new BenefitReceiveRequest(), param);
        request.setReceiver(param.getReceiver().getMemberId());
        if(Objects.nonNull(param.getHolder())){
            request.setUser(param.getHolder().getMemberId());
        }
        request.setIds(param.getCoupons()
                .stream()
                .map(c->new TicketIdentity(c.getId(), c.getCode(), c.getProjectId()))
                .collect(Collectors.toList()));
        return request;
    }

    @Override
    public BenefitReceiveResponse request(BenefitReceiveRequest invokeParam) {
        return benefitService.receive(invokeParam);
    }

    @Override
    public Void prepareResult(CouponReceiveParam param, BenefitReceiveResponse result) {
        if(TransactionStatus.SUCCESS.getCode().equals(result.getStatus().getCode())){
            return null;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500240, result.getMessage());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_RECEIVE;
    }
}
