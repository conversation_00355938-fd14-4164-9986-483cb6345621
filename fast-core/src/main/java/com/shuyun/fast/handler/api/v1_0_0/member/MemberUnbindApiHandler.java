package com.shuyun.fast.handler.api.v1_0_0.member;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.cache.v1_0_0.MbspCache;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.util.DateUtil;
import com.shuyun.fast.v1_0_0.domain.MemberId;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.param.member.MemberUnbindParam;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.kylin.member.api.request.ChannelMemberUnbindRequest;
import com.shuyun.kylin.member.api.response.MemberBaseResponse;
import com.shuyun.kylin.member.api.response.MemberBaseResult;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Singleton
public class MemberUnbindApiHandler extends AbstractApiHandler<MemberUnbindParam, MemberId, ChannelMemberUnbindRequest, MemberBaseResult> {
    private final MemberService memberService;

    public MemberUnbindApiHandler(MemberService memberService) {
        this.memberService = memberService;
    }
    @Override
    public void validate(MemberUnbindParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.bothRequiredValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public MemberUnbindParam beforeRequest(MemberUnbindParam param) {
        super.beforeRequest(param);
        return param;
    }

    @Override
    public ChannelMemberUnbindRequest prepareParam(MemberUnbindParam param) {
        MbspCache cache = memberService.bizCacheGet(param);
        ChannelMemberUnbindRequest request = new ChannelMemberUnbindRequest();
        request.setProgramCode(cache.getProgramId());
        request.setCustomerNo(param.getIdentify().getUserId());
        request.setUnbindTime(DateUtil.utcTime(param.getUnbindTime()));
        request.setEnrollChannel(param.getRequestChannel());
        if(StringUtils.isNotEmpty(param.getRegisterShopCode())){
            request.setEnrollShopCode(param.getRegisterShopCode());
        }
        return request;
    }

    @Override
    public MemberBaseResult request(ChannelMemberUnbindRequest invokeParam) {
        return memberService.unbind(invokeParam);
    }

    @Override
    public MemberId prepareResult(MemberUnbindParam param, MemberBaseResult result) {
        if(StringUtils.isEmpty(result.getError_code()) && Objects.nonNull(result.getData())){
            MemberBaseResponse response = result.getData();
            MemberId memberId = new MemberId();
            memberId.setMemberId(response.getMemberId());
            return memberId;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500202, result.getMsg());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_UNBIND;
    }
}
