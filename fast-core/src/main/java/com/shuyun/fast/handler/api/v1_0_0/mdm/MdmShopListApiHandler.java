package com.shuyun.fast.handler.api.v1_0_0.mdm;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.MdmService;
import com.shuyun.fast.v1_0_0.domain.MdmShop;
import com.shuyun.fast.v1_0_0.param.mdm.MdmShopListParam;
import com.shuyun.fast.validator.FastValidator;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

@Slf4j
@Singleton
public class MdmShopListApiHandler extends AbstractApiHandler<MdmShopListParam, PageResult<MdmShop>, MdmShopListParam, PageResult<MdmShop>> {

    private final MdmService mdmService;

    public MdmShopListApiHandler(MdmService mdmService){
        this.mdmService = mdmService;
    }

    @Override
    public void validate(MdmShopListParam param) {
        super.validate(param);
        LocalDateTime startTime = param.getStartTime();
        LocalDateTime endTime = param.getEndTime();
        FastValidator.bothRequiredValidate(startTime, endTime, "startTime", "endTime");
        FastValidator.timeIntervalValidate(startTime, endTime, 1L, TimeUnit.DAYS);
    }


    @Override
    public MdmShopListParam beforeRequest(MdmShopListParam param) {
        super.beforeRequest(param);
        return param;
    }

    @Override
    public MdmShopListParam prepareParam(MdmShopListParam param) {
        return param;
    }

    @Override
    public PageResult<MdmShop> request(MdmShopListParam invokeParam) {
        return mdmService.shopList(invokeParam);
    }

    @Override
    public PageResult<MdmShop> prepareResult(MdmShopListParam param, PageResult<MdmShop> result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MDM_SHOP_LIST;
    }
}
