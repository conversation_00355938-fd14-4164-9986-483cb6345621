package com.shuyun.fast.handler.api.v1_0_0.member;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.param.member.MemberRegisterParam;
import com.shuyun.fast.v1_0_0.result.MemberRegisterResult;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.kylin.member.api.response.RegisterResponse;
import com.shuyun.kylin.member.api.response.RegisterResult;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Singleton
public class MemberRegisterApiHandler extends AbstractApiHandler<MemberRegisterParam, MemberRegisterResult, MemberRegisterParam, RegisterResult> {

    private final MemberService memberService;

    public MemberRegisterApiHandler(MemberService memberService) {
        this.memberService = memberService;
    }

    @Override
    public void validate(MemberRegisterParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.singleEmptyValidate(memberId,  "memberId");
        FastValidator.singleRequiredValidate(userId,  "userId");
    }

    @Override
    public MemberRegisterParam beforeRequest(MemberRegisterParam param) {
        super.beforeRequest(param);
        param.setRegisterChannel(param.getRequestChannel());
        return param;
    }

    @Override
    public MemberRegisterParam prepareParam(MemberRegisterParam param) {
        return param;
    }

    @Override
    public RegisterResult request(MemberRegisterParam invokeParam) {
        return memberService.register(invokeParam);
    }

    @Override
    public MemberRegisterResult prepareResult(MemberRegisterParam param, RegisterResult result) {
        if(StringUtils.isEmpty(result.getError_code()) && Objects.nonNull(result.getData())){
            RegisterResponse registerResponse = result.getData();
            MemberRegisterResult r = new MemberRegisterResult();
            r.setMemberId(registerResponse.getMemberId());
            r.setStatus(registerResponse.getStatus());
            return r;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500201, result.getMsg());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_REGISTER;
    }
}
