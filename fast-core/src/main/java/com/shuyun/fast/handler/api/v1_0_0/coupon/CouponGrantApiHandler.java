package com.shuyun.fast.handler.api.v1_0_0.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.BenefitService;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.domain.Coupon;
import com.shuyun.fast.v1_0_0.param.coupon.CouponGrantParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.ticket.benefit.vo.response.benefit.BenefitGrantResponse;
import com.shuyun.ticket.enums.TransactionStatus;
import io.micronaut.context.annotation.Requires;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Singleton
@Requires(property = "benefit.enable", value = "true", defaultValue = "false")
public class CouponGrantApiHandler extends AbstractApiHandler<CouponGrantParam, List<Coupon>, CouponGrantParam, BenefitGrantResponse> {

    private final BenefitService benefitService;
    private final MemberService memberService;
    public CouponGrantApiHandler(MemberService memberService,
                                 BenefitService benefitService){
        this.memberService = memberService;
        this.benefitService = benefitService;
    }

    @Override
    public void validate(CouponGrantParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        if(Objects.nonNull(identify)){
            String memberId = identify.getMemberId();
            String userId = identify.getUserId();
            FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
        }
    }

    @Override
    public CouponGrantParam beforeRequest(CouponGrantParam param) {
        super.beforeRequest(param);
        if(Objects.nonNull(param.getIdentify())){
            param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        }
        param.setGrantPlatform(param.getRequestChannel());
        return param;
    }

    @Override
    public CouponGrantParam prepareParam(CouponGrantParam param) {
        return param;
    }

    @Override
    public BenefitGrantResponse request(CouponGrantParam invokeParam) {
        return benefitService.grant(invokeParam);
    }

    @Override
    public List<Coupon> prepareResult(CouponGrantParam param, BenefitGrantResponse result) {
        if(TransactionStatus.SUCCESS.getCode().equals(result.getStatus().getCode())){
            List<Coupon> coupons = result.getSuccess()
                    .stream()
                    .map(c->{
                        Coupon coupon = JsonUtil.outPutConvert(c, Coupon.class);
                        return coupon;
                    })
                    .collect(Collectors.toList());
            if(coupons.size() < param.getGrantNum()){
                log.warn("return codes number:{} less than request number:{}", coupons.size(), param.getGrantNum());
            }
            return coupons;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500234, result.getMessage());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_GRANT;
    }
}
