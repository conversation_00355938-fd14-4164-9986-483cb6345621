package com.shuyun.fast.handler.api.v1_0_0.point;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.service.v1_0_0.PointService;
import com.shuyun.fast.v1_0_0.domain.Point;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.param.point.PointGetParam;
import com.shuyun.fast.validator.FastValidator;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class PointGetApiHandler extends AbstractApiHandler<PointGetParam, Point, PointGetParam, Point> {

    private final MemberService memberService;
    private final PointService pointService;
    public PointGetApiHandler(MemberService memberService,
                              PointService pointService){
        this.memberService = memberService;
        this.pointService = pointService;
    }


    @Override
    public void validate(PointGetParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public PointGetParam beforeRequest(PointGetParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public PointGetParam prepareParam(PointGetParam param) {
        return param;
    }

    @Override
    public Point request(PointGetParam invokeParam) {
        return pointService.pointGet(invokeParam);
    }

    @Override
    public Point prepareResult(PointGetParam param, Point result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_POINT_GET;
    }
}
