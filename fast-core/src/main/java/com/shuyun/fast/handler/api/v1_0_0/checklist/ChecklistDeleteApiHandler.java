package com.shuyun.fast.handler.api.v1_0_0.checklist;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.ChecklistService;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.param.checklist.ChecklistDeleteParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.validator.FastValidator;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Singleton
public class ChecklistDeleteApiHandler extends AbstractApiHandler<ChecklistDeleteParam, Void, ChecklistDeleteParam, Void> {

    private final ChecklistService checklistService;
    private final MemberService memberService;
    public ChecklistDeleteApiHandler(ChecklistService checklistService,
                                  MemberService memberService) {
        this.checklistService = checklistService;
        this.memberService = memberService;
    }

    @Override
    public void validate(ChecklistDeleteParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        if(Objects.nonNull(identify)){
            String memberId = identify.getMemberId();
            String userId = identify.getUserId();
            FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
        }
    }

    @Override
    public ChecklistDeleteParam prepareParam(ChecklistDeleteParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public Void request(ChecklistDeleteParam invokeParam) {
        return checklistService.delete(invokeParam);
    }

    @Override
    public Void prepareResult(ChecklistDeleteParam param, Void result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_CHECKLIST_DELETE;
    }
}
