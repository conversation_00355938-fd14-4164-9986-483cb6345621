package com.shuyun.fast.handler.api.v1_0_0.medal;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.GradeService;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.domain.Medal;
import com.shuyun.fast.v1_0_0.param.medal.MedalGetParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.validator.FastValidator;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


@Slf4j
@Singleton
public class MedalGetApiHandler extends AbstractApiHandler<MedalGetParam, List<Medal>, MedalGetParam, List<Medal>> {
    private final GradeService gradeService;
    private final MemberService memberService;

    public MedalGetApiHandler(MemberService memberService,
                              GradeService gradeService){
        this.memberService = memberService;
        this.gradeService = gradeService;
    }

    @Override
    public void validate(MedalGetParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public MedalGetParam beforeRequest(MedalGetParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public MedalGetParam prepareParam(MedalGetParam param) {
        return param;
    }

    @Override
    public List<Medal> request(MedalGetParam invokeParam) {
        return gradeService.medalGet(invokeParam);
    }

    @Override
    public List<Medal> prepareResult(MedalGetParam param, List<Medal> result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEDAL_GET;
    }
}
