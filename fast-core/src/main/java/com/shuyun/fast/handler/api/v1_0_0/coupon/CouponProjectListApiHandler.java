package com.shuyun.fast.handler.api.v1_0_0.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.BenefitService;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.domain.CouponProject;
import com.shuyun.fast.v1_0_0.param.coupon.CouponProjectListParam;
import com.shuyun.ticket.benefit.domain.BenefitProject;
import com.shuyun.ticket.benefit.vo.Page;
import com.shuyun.ticket.benefit.vo.request.project.ProjectQueryVo;
import io.micronaut.context.annotation.Requires;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Singleton
@Requires(property = "benefit.enable", value = "true", defaultValue = "false")
public class CouponProjectListApiHandler extends AbstractApiHandler<CouponProjectListParam, Page<CouponProject>, CouponProjectListParam, Page<Map>> {
    private final BenefitService benefitService;
    public CouponProjectListApiHandler(BenefitService benefitService){
        this.benefitService = benefitService;
    }

    @Override
    public void validate(CouponProjectListParam param) {
        super.validate(param);
    }

    @Override
    public CouponProjectListParam beforeRequest(CouponProjectListParam param) {
        super.beforeRequest(param);
        param.setSortBy(param.getSortBy().toLowerCase());
        param.setSortType(param.getSortType().toLowerCase());
        return param;
    }

    @Override
    public CouponProjectListParam prepareParam(CouponProjectListParam param) {
        return param;
    }

    @Override
    public Page<Map> request(CouponProjectListParam invokeParam) {
        return benefitService.projectList(invokeParam);
    }

    @Override
    public Page<CouponProject> prepareResult(CouponProjectListParam param, Page<Map> result) {

        if(Objects.nonNull(result)){
            Page<CouponProject> pageProjects = new Page<CouponProject>();
            pageProjects.setPage(result.getPage());
            pageProjects.setPageSize(result.getPageSize());
            pageProjects.setTotal(result.getTotal());
            pageProjects.setData(CollectionUtils.isEmpty(result.getData())? Collections.emptyList():
                    result.getData().stream().map(bp->JsonUtil.outPutConvert(bp, CouponProject.class)).collect(Collectors.toList()));
            return pageProjects;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500242);
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_PROJECT_LIST;
    }
}
