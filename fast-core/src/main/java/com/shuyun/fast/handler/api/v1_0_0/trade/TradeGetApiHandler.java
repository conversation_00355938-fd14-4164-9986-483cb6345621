package com.shuyun.fast.handler.api.v1_0_0.trade;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.service.v1_0_0.TradeService;
import com.shuyun.fast.v1_0_0.domain.TradeMainOrder;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.param.trade.TradeGetParam;
import com.shuyun.fast.validator.FastValidator;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class TradeGetApiHandler extends AbstractApiHandler<TradeGetParam, PageResult<TradeMainOrder>, TradeGetParam, PageResult<TradeMainOrder>> {

    private final TradeService tradeService;

    private final MemberService memberService;

    public TradeGetApiHandler(TradeService tradeService,
                                     MemberService memberService){
        this.tradeService = tradeService;
        this.memberService = memberService;
    }

    @Override
    public void validate(TradeGetParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public TradeGetParam beforeRequest(TradeGetParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public TradeGetParam prepareParam(TradeGetParam param) {
        // TODO: 2024/3/29 时区转换
        return param;
    }

    @Override
    public PageResult<TradeMainOrder> request(TradeGetParam invokeParam) {
        return tradeService.get(invokeParam);
    }

    @Override
    public PageResult<TradeMainOrder> prepareResult(TradeGetParam param, PageResult<TradeMainOrder> result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_TRADE_GET;
    }
}
