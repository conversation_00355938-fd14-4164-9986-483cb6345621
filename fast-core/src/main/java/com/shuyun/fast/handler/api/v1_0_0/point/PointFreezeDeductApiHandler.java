package com.shuyun.fast.handler.api.v1_0_0.point;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.cache.v1_0_0.PointCache;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.service.v1_0_0.PointService;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.param.point.PointFreezeDeductParam;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointUseFrozenRequest;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class PointFreezeDeductApiHandler extends AbstractApiHandler<PointFreezeDeductParam, Void, MemberPointUseFrozenRequest, Void> {

    private final MemberService memberService;
    private final PointService pointService;
    public PointFreezeDeductApiHandler(MemberService memberService,
                                       PointService pointService){
        this.memberService = memberService;
        this.pointService = pointService;
    }

    @Override
    public void validate(PointFreezeDeductParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public PointFreezeDeductParam beforeRequest(PointFreezeDeductParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public MemberPointUseFrozenRequest prepareParam(PointFreezeDeductParam param) {
        PointCache cache = pointService.bizCacheGet(param, param.getPointBizType());
        MemberPointUseFrozenRequest request = new MemberPointUseFrozenRequest();
        request.setChannelType(param.getRequestChannel());
        request.setMemberId(param.getIdentify().getMemberId());
        request.setActionName(param.getActionName());
        request.setDesc(param.getDescription());
        request.setPointAccountId(cache.getPointAccountTypeId());
        request.setShopId(param.getShopCode());
        request.setBusinessId(param.getFreezeTransactionId());
        request.setKzzd1(param.getKZZD1());
        request.setKzzd2(param.getKZZD2());
        request.setKzzd3(param.getKZZD3());
        request.setTx(param.getReversible());
        return request;
    }

    @Override
    public Void request(MemberPointUseFrozenRequest invokeParam) {
        return pointService.freezeDeduct(invokeParam);
    }

    @Override
    public Void prepareResult(PointFreezeDeductParam param, Void result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_POINT_FREEZE_DEDUCT;
    }
}
