package com.shuyun.fast.handler.event;

public abstract class AbstractEventHandler<E> {

    public void validate(E event) {

    }

    public void beforeHandle(E event) {

    }

    public abstract void doHandle(E event) throws Exception;

    public void afterHandle(E event) {

    }

    public void handle(E event) throws Exception{
        validate(event);
        beforeHandle(event);
        doHandle(event);
        afterHandle(event);
    }
}
