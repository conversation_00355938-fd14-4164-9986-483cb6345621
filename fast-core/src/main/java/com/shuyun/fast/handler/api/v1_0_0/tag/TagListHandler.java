package com.shuyun.fast.handler.api.v1_0_0.tag;

import com.shuyun.cdp.common.response.PageableResponse;
import com.shuyun.cdp.tags.response.openapi.TagListResponse;
import com.shuyun.cdp.tags.vo.openapi.OpenTagVo;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.TagService;
import com.shuyun.fast.v1_0_0.param.tag.TagListParam;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class TagListHandler extends AbstractApiHandler<TagListParam, PageResult<OpenTagVo>, TagListParam, TagListResponse> {

    private final TagService tagService;
    public TagListHandler(TagService tagService){
        this.tagService = tagService;
    }

    @Override
    public void validate(TagListParam param) {
        super.validate(param);
    }

    @Override
    public TagListParam beforeRequest(TagListParam param) {
        super.beforeRequest(param);
        return param;
    }

    @Override
    public TagListParam prepareParam(TagListParam param) {
        return param;
    }

    @Override
    public TagListResponse request(TagListParam invokeParam) {
        return tagService.list(invokeParam);
    }

    @Override
    public PageResult<OpenTagVo> prepareResult(TagListParam param, TagListResponse result) {
        if(ApiTags.API_RESP_SUCCESS.equals(result.getResponseCode())){
            PageableResponse.PageInfo<OpenTagVo> tagPage = result.getData();
            PageResult<OpenTagVo> pageResult = new PageResult<>();
            pageResult.setPage(tagPage.getPageNo());
            pageResult.setPageSize(tagPage.getPageSize());
            pageResult.setTotalCount(tagPage.getTotalElement());
            pageResult.setItems(tagPage.getContent());
            return pageResult;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500401, result.getResponseMsg());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_TAG_LIST;
    }
}
