package com.shuyun.fast.handler.api.v1_0_0.grade;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.GradeService;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.param.grade.GradeBudgetParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.result.GradeBudgetResult;
import com.shuyun.fast.validator.FastValidator;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Singleton
public class GradeBudgetApiHandler extends AbstractApiHandler<GradeBudgetParam, GradeBudgetResult, GradeBudgetParam, GradeBudgetResult> {
    private final GradeService gradeService;
    private final MemberService memberService;

    public GradeBudgetApiHandler(MemberService memberService,
                                 GradeService gradeService){
        this.memberService = memberService;
        this.gradeService = gradeService;
    }

    @Override
    public void validate(GradeBudgetParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public GradeBudgetParam beforeRequest(GradeBudgetParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public GradeBudgetParam prepareParam(GradeBudgetParam param) {
        return param;
    }

    @Override
    public GradeBudgetResult request(GradeBudgetParam invokeParam) {
        return gradeService.gradeBudget(invokeParam);
    }

    @Override
    public GradeBudgetResult prepareResult(GradeBudgetParam param, GradeBudgetResult result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_GRADE_BUDGET;
    }
}
