package com.shuyun.fast.handler.api.v1_0_0.tag;

import com.shuyun.cdp.common.response.BaseResponse;
import com.shuyun.cdp.tags.request.openapi.CustomerTagsOperateRequest;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.service.v1_0_0.TagService;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.param.tag.TagUntaggingParam;
import com.shuyun.fast.validator.FastValidator;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Singleton
public class TagUntaggingHandler extends AbstractApiHandler<TagUntaggingParam, Void, TagUntaggingParam, BaseResponse<String>> {



    private final TagService tagService;
    private final MemberService memberService;
    public TagUntaggingHandler(TagService tagService,
                             MemberService memberService){
        this.tagService = tagService;
        this.memberService = memberService;
    }


    @Override
    public void validate(TagUntaggingParam param) {
        super.validate(param);
        List<String> memberIds = param.getMemberIds();
        List<CustomerTagsOperateRequest.OperateTag> tags = param.getTags();
        String tagId = param.getTagId();
        FastValidator.eitherOrValidate(tagId, tags, "tagId", "tags");
        if(CollectionUtils.isNotEmpty(memberIds)){
            FastValidator.singleEmptyValidate(tags, "tags");
            FastValidator.singleEmptyValidate(param.getIdentify(), "identify");
        }else{
            FastValidator.singleEmptyValidate(memberIds, "memberIds");
            FastValidator.singleRequiredValidate(param.getIdentify(), "identify");

            MemberIdentifyParam identify = param.getIdentify();
            String memberId = identify.getMemberId();
            String userId = identify.getUserId();
            FastValidator.bothRequiredValidate(memberId, userId, "memberId","userId");
        }
    }

    @Override
    public TagUntaggingParam beforeRequest(TagUntaggingParam param) {
        super.beforeRequest(param);
        if(!param.getBatchUntagging()){
            param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        }
        return param;
    }

    @Override
    public TagUntaggingParam prepareParam(TagUntaggingParam param) {
        return param;
    }

    @Override
    public BaseResponse<String> request(TagUntaggingParam invokeParam) {
        return tagService.untagging(invokeParam);
    }

    @Override
    public Void prepareResult(TagUntaggingParam param, BaseResponse<String> result) {
        if(ApiTags.API_RESP_SUCCESS.equals(result.getResponseCode())){
            return null;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500403, result.getResponseMsg());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_TAG_UNTAGGING;
    }
}
