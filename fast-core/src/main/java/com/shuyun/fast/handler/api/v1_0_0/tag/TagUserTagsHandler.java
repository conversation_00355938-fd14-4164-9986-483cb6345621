package com.shuyun.fast.handler.api.v1_0_0.tag;

import com.shuyun.cdp.tags.response.openapi.CustomerTagQueryResponse;
import com.shuyun.cdp.tags.vo.openapi.CustomerTagsVo;
import com.shuyun.cdp.tags.vo.openapi.OpenTagContent;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.service.v1_0_0.TagService;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.param.tag.TagUserTagsParam;
import com.shuyun.fast.validator.FastValidator;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import java.util.Collections;
import java.util.List;

@Slf4j
@Singleton
public class TagUserTagsHandler extends AbstractApiHandler<TagUserTagsParam, List<OpenTagContent>, TagUserTagsParam, CustomerTagQueryResponse> {
    private final TagService tagService;
    private final MemberService memberService;
    public TagUserTagsHandler(TagService tagService,
                             MemberService memberService){
        this.tagService = tagService;
        this.memberService = memberService;
    }

    @Override
    public void validate(TagUserTagsParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.bothRequiredValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public TagUserTagsParam beforeRequest(TagUserTagsParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public TagUserTagsParam prepareParam(TagUserTagsParam param) {
        return param;
    }

    @Override
    public CustomerTagQueryResponse request(TagUserTagsParam invokeParam) {
        return tagService.userTags(invokeParam);
    }

    @Override
    public List<OpenTagContent> prepareResult(TagUserTagsParam param, CustomerTagQueryResponse result) {
        if(ApiTags.API_RESP_SUCCESS.equals(result.getResponseCode())){
            String memberId = param.getIdentify().getMemberId();
            for(CustomerTagsVo cs:result.getData()){
                if(memberId.equals(cs.getOriginId())){
                    return cs.getTags();
                }
            }
            return Collections.emptyList();
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500404, result.getResponseMsg());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_TAG_USER_TAGS;
    }
}
