package com.shuyun.fast.handler.api.v1_0_0.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.BenefitService;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.util.DateUtil;
import com.shuyun.fast.v1_0_0.domain.Coupon;
import com.shuyun.fast.v1_0_0.param.coupon.CouponUnlockParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.ticket.base.resource.dto.TicketIdentity;
import com.shuyun.ticket.benefit.vo.request.benefit.BenefitUnlockRequest;
import com.shuyun.ticket.benefit.vo.response.benefit.BenefitUnlockResponse;
import com.shuyun.ticket.enums.TransactionStatus;
import com.shuyun.ticket.util.JsonUtil;
import io.micronaut.context.annotation.Requires;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Singleton
@Requires(property = "benefit.enable", value = "true", defaultValue = "false")
public class CouponUnlockApiHandler extends AbstractApiHandler<CouponUnlockParam, List<Coupon>, BenefitUnlockRequest, BenefitUnlockResponse> {

    private final BenefitService benefitService;
    private final MemberService memberService;
    public CouponUnlockApiHandler(MemberService memberService,
                                  BenefitService benefitService){
        this.memberService = memberService;
        this.benefitService = benefitService;
    }

    @Override
    public void validate(CouponUnlockParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        if(Objects.nonNull(identify)){
            String memberId = identify.getMemberId();
            String userId = identify.getUserId();
            FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
        }
    }

    @Override
    public CouponUnlockParam beforeRequest(CouponUnlockParam param) {
        super.beforeRequest(param);
        if(Objects.nonNull(param.getIdentify())){
            param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        }
        return param;
    }

    @Override
    public BenefitUnlockRequest prepareParam(CouponUnlockParam param) {
        BenefitUnlockRequest request = benefitService.setSceneParam(new BenefitUnlockRequest(), param);
        if(Objects.nonNull(param.getIdentify())){
            request.setUser(param.getIdentify().getMemberId());
        }
        request.setIds(param.getCoupons()
                .stream()
                .map(c->new TicketIdentity(c.getId(), c.getCode(), c.getProjectId()))
                .collect(Collectors.toList()));
        request.setLockTransactionId(param.getLockTransactionId());
        return request;
    }

    @Override
    public BenefitUnlockResponse request(BenefitUnlockRequest invokeParam) {
        return benefitService.unLock(invokeParam);
    }

    @Override
    public List<Coupon> prepareResult(CouponUnlockParam param, BenefitUnlockResponse result) {
        if(TransactionStatus.SUCCESS.getCode().equals(result.getStatus().getCode())){
            List<Coupon> coupons = result.getSuccess()
                    .stream()
                    .map(c->{
                        Coupon coupon = JsonUtil.copyValue(c, new Coupon());
                        return coupon;
                    })
                    .collect(Collectors.toList());
            if(coupons.size() < param.getCoupons().size()){
                log.warn("return codes number:{} less than request number:{}", coupons.size(), param.getCoupons().size());
            }
            return coupons;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500237, result.getMessage());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_UNLOCK;
    }
}
