package com.shuyun.fast.handler.api.v1_0_0.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.BenefitService;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.param.coupon.CouponListParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.result.CouponGetResult;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.ticket.benefit.vo.Page;
import io.micronaut.context.annotation.Requires;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Requires(property = "benefit.enable", value = "true", defaultValue = "false")
public class CouponListApiHandler extends AbstractApiHandler<CouponListParam, Page<CouponGetResult>, CouponListParam, Page<CouponGetResult>> {
    private final BenefitService benefitService;
    private final MemberService memberService;

    public CouponListApiHandler(MemberService memberService,
                                BenefitService benefitService){
        this.memberService = memberService;
        this.benefitService = benefitService;
    }

    @Override
    public void validate(CouponListParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public CouponListParam beforeRequest(CouponListParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public CouponListParam prepareParam(CouponListParam param) {
        return param;
    }

    @Override
    public Page<CouponGetResult> request(CouponListParam invokeParam) {
        return benefitService.pageList(invokeParam);
    }

    @Override
    public Page<CouponGetResult> prepareResult(CouponListParam param, Page<CouponGetResult> result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_LIST;
    }
}
