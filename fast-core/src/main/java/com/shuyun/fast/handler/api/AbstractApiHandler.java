package com.shuyun.fast.handler.api;
import com.shuyun.fast.base.ApiTag;
import lombok.extern.slf4j.Slf4j;

/**
 * 本类主要定义业务流转,做一些诸如限流类的公共逻辑控制，尽量不侵入接口业务逻辑
 * @param <P> param:api接口入参
 * @param <R> result:api接口响应
 * @param <IP> invoke param:rpc接口入参
 * @param <IR> invoke result:rpc接口响应
 */
@Slf4j
public abstract class AbstractApiHandler<P, R, IP, IR> implements ApiTag{

    public void validate(P param) {

    }

    // TODO: 2024/3/5 限流检查、memberId替换等公共事项
    public P beforeRequest(P param) {

        return param;

    }

    public abstract IP prepareParam(P param);

    public abstract IR request(IP invokeParam);

    public abstract R prepareResult(P param, IR result);

    public R handle(P param) {
        validate(param);
        P replacedParam = beforeRequest(param);
        return prepareResult(replacedParam, request(prepareParam(replacedParam)));
    }

}
