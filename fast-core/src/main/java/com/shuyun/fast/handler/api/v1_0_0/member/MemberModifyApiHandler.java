package com.shuyun.fast.handler.api.v1_0_0.member;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.cache.v1_0_0.MbspCache;
import com.shuyun.fast.exception.ApiException;
import com.shuyun.fast.handler.api.AbstractApiHandler;
import com.shuyun.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.util.DateUtil;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.domain.MemberId;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.param.member.MemberModifyParam;
import com.shuyun.fast.validator.FastValidator;
import com.shuyun.kylin.member.api.request.ChannelMemberEditFilter;
import com.shuyun.kylin.member.api.request.ChannelMemberEditParams;
import com.shuyun.kylin.member.api.request.ChannelMemberEditRequest;
import com.shuyun.kylin.member.api.request.MemberEditRequest;
import com.shuyun.kylin.member.api.response.MemberBaseResponse;
import com.shuyun.kylin.member.api.response.MemberBaseResult;
import com.shuyun.kylin.member.api.response.MemberUpdateResult;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Singleton
public class MemberModifyApiHandler extends AbstractApiHandler<MemberModifyParam, MemberId, ChannelMemberEditRequest, MemberUpdateResult> {

    private final MemberService memberService;

    public MemberModifyApiHandler(MemberService memberService) {
        this.memberService = memberService;
    }

    @Override
    public void validate(MemberModifyParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public MemberModifyParam beforeRequest(MemberModifyParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        param.setChannelType(param.getRequestChannel());
        return param;
    }

    @Override
    public ChannelMemberEditRequest prepareParam(MemberModifyParam param) {
        MbspCache cache = memberService.bizCacheGet(param);
        ChannelMemberEditRequest request = new ChannelMemberEditRequest();
        request.setProgramCode(cache.getProgramId());
        request.setMemberId(param.getIdentify().getMemberId());
        request.setEnrollChannel(param.getRequestChannel());
        ChannelMemberEditParams editParams = JsonUtil.filterConvert(param,
                ChannelMemberEditParams.class,
                "memberModifyParamFilter",
                "modifyTime");
        // TODO: 2024/3/15 时区转换
        editParams.setModifyTime(DateUtil.utcTime(param.getModifyTime()));
        request.setParams(editParams);
        ChannelMemberEditFilter filter = new ChannelMemberEditFilter();
        if(StringUtils.isNotEmpty(param.getIdentify().getUserId())){
            filter.setCustomerNo(param.getIdentify().getUserId());
            request.setFilter(filter);
        }
        return request;
    }

    @Override
    public MemberUpdateResult request(ChannelMemberEditRequest invokeParam) {
        return memberService.modify(invokeParam);
    }

    @Override
    public MemberId prepareResult(MemberModifyParam param, MemberUpdateResult result) {
        if(StringUtils.isEmpty(result.getError_code()) && Objects.nonNull(result.getData())){
            MemberBaseResponse response = result.getData();
            MemberId memberId = new MemberId();
            memberId.setMemberId(response.getMemberId());
            return memberId;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500205, result.getMsg());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_MODIFY;
    }
}
