package com.shuyun.fast.event.client;

import com.shuyun.fast.event.domain.v1_0_0.BenefitProjectEvent;
import com.shuyun.fast.event.domain.v1_0_0.EsEvent;
import com.shuyun.fast.kafka.KafkaSender;
import com.shuyun.fast.service.v1_0_0.BenefitService;
import com.shuyun.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.param.coupon.CouponProjectGetParam;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.annotation.Value;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Singleton
@Requires(property = "benefit.enable", value = "true", defaultValue = "false")
public class BenefitProjectEventListener extends EsEventListener<BenefitProjectEvent> {

    public BenefitProjectEventListener(KafkaSender KafkaSender){
        super(KafkaSender, 15, 10);
    }

    @Value("${micronaut.application.name}")
    private String serviceId;

    @Override
    public BenefitProjectEvent type() {
        return new BenefitProjectEvent();
    }

    @Override
    public String currentService() {
        return serviceId;
    }
}
