package com.shuyun.fast.event.client;

import com.shuyun.es.sdk.domain.Event;
import com.shuyun.es.sdk.domain.EventSendResponse;
import com.shuyun.es.sdk.factory.EventServiceSdkFactory;
import com.shuyun.es.sdk.service.EventService;
import com.shuyun.fast.util.JsonUtil;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Singleton
@Slf4j
public class EsEventSender {


    private EventService eventService;


    @PostConstruct
    public void register(){
        eventService = EventServiceSdkFactory.byDescovery();
        log.info("es event sender:{} register success", this.getClass().getName());
    }

    public <T> EventSendResponse send(String fqn, T event){
        List<Map<String, Object>> data = Collections.singletonList(JsonUtil.convert(event, Map.class));
        Event e = new Event();
        e.setData(data);
        return eventService.sendEvent(fqn, e);
    }

}
