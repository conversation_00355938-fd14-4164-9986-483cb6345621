package com.shuyun.fast.event.domain.v1_0_0;

import com.shuyun.fast.base.ModelTags;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BenefitProjectEvent extends EsEvent{
    private String id;
    private String projectId;
    private String contentId;
    private String version;
    private Boolean createNow;
    private Boolean activateNow;
    private String eventType;
    private String objectId;
    private String occurrenceDt;
    private String createTime;
    private String detectionDt;
    private String serviceId;
    @Override
    public String partitionKey() {
        return projectId;
    }

    @Override
    public String routeTopic() {
        return ModelTags.EVENT_TOPIC_BENEFIT_PROJECT;
    }

    @Override
    public void eventDispatcherService(String serviceId) {
        this.serviceId = serviceId;
    }

    @Override
    public String fqn() {
        return ModelTags.EVENT_FQN_BENEFIT_PROJECT;
    }
}
