package com.shuyun.fast.ser;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class UtcTimeToLocalTimeDeserializer extends JsonDeserializer<LocalTime> {
    private final static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");

    @Override
    public LocalTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String timeString = p.getText();
        LocalTime localTime;
        localTime = LocalTime.parse(timeString, formatter);
        return localTime.plusHours(8);
    }
}
