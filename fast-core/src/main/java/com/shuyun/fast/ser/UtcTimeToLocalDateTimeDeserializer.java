package com.shuyun.fast.ser;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class UtcTimeToLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {

    private final static DateTimeFormatter utcFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
    private final static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String timeString = p.getText();
        LocalDateTime localTime;
        if(timeString.contains("T")){
            ZonedDateTime utcTime = ZonedDateTime.parse(timeString, utcFormatter).withZoneSameInstant(ZoneId.of("UTC"));
            ZonedDateTime  localDateTime = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
            localTime = localDateTime.toLocalDateTime();
        }else{
            localTime = LocalDateTime.parse(timeString, formatter);
        }

        return localTime;
    }
}
