package com.shuyun.fast.ser;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class OutputZonedDateTimeSerializer extends JsonSerializer<ZonedDateTime> {
    private final static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Override
    public void serialize(ZonedDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value != null) {
            ZonedDateTime zonedDateTime = value.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
            gen.writeString(zonedDateTime.format(formatter));
        }
    }
}
