package com.shuyun.fast.repository;
//
//import com.shuyun.fast.entity.BizCache;
//import io.micronaut.data.jdbc.annotation.JdbcRepository;
//import io.micronaut.data.model.query.builder.sql.Dialect;
//import io.micronaut.data.repository.CrudRepository;
//
//import java.util.Optional;
//
//@JdbcRepository(dialect = Dialect.MYSQL)
//public interface BizCacheRepository extends CrudRepository<BizCache, Long>{
//
//    Optional<BizCache> find(String tenantId, String bizCode, String cacheType);
//}

import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.fast.util.DataapiSdkUtil;
import com.shuyun.fast.util.JsonUtil;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Singleton;
import com.shuyun.fast.entity.BizCache;
import java.util.*;

@Singleton
public class BizCacheRepository {

    final static DataapiHttpSdk dataapiHttpSdk = DataapiSdkUtil.getDataapiHttpSdk();

    public BizCacheRepository(){
    }

    public Optional<BizCache> find(String tenantId, String bizCode, String cacheType){
        String sql = "select tenantId,bizCode,cacheType,value,createTime,updateTime from %s where tenantId = :tenantId and bizCode = :bizCode and cacheType = :cacheType";
        String fqn = "data.prctvmkt.fast.BizCache";
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", tenantId);
        params.put("bizCode", bizCode);
        params.put("cacheType", cacheType);
        BaseResponse<Map> response = dataapiHttpSdk.execute(String.format(sql, fqn), params);
        if(CollectionUtils.isNotEmpty(response.getData())){
            return Optional.of(JsonUtil.convert(response.getData().get(0), BizCache.class));
        }
        return Optional.empty();
    }

    public List<BizCache> findAll(){
        String sql = "select tenantId,bizCode,cacheType,value,createTime,updateTime from %s";
        String fqn = "data.prctvmkt.fast.BizCache";
        BaseResponse<Map> response = dataapiHttpSdk.execute(String.format(sql, fqn), new HashMap<>());
        if(CollectionUtils.isNotEmpty(response.getData())){
            List<BizCache> list = new ArrayList<>(response.getData().size());
            for (Map map : response.getData()) {
                list.add(JsonUtil.convert(map, BizCache.class));
            }
            return list;
        }
        return Collections.emptyList();
    }
}
