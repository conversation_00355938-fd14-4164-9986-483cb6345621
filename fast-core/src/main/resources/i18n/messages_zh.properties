000000=success
100000=api path error
100001=required param error[{0}]
100002=member identify param error[{0} and {1} can not be all empty.]
100003=member identify param error[{0} or {1} must be empty.]
100004=member identify param error[{0} and {1} both required.]
100005=member identify param error[{0} must be empty.]
100006=member identify param error[{0} must not be empty.]
100007=time pair param error[the {0} must be less than {1}.]
100008=time pair param error[the time interval between {0} and {1} must be less than {2} {3}.]
100009=member identify param error[{0} and {1} and {2} can not be all empty.]
100010=member identify param error[the three param {0},{1} and {2} only one required.]

500000=biz error[found no member by userId:{0}]
500001=biz error[found no {0} biz cache config]


500100=server error[{0}]
500200=member bind scene error[{0}]
500201=member register scene error[{0}]
500202=member unbind scene error[{0}]
500203=member query scene error[{0}]
500204=member deregister scene error[{0}]
500205=member modify scene error[{0}]
500206=member mobile modify scene error[{0}]
500207=member dynamic code get scene error[{0}]
500208=member dynamic code identify scene error[{0}]


500230=coupon consume scene error[{0}]
500231=coupon consume repeal scene error[{0}]
500232=coupon discount calc scene error[{0}]
500233=coupon get scene error[found no coupon by code:{0}]
500234=coupon grant scene error[{0}]
500235=coupon grant repeal scene error[{0}]
500236=coupon lock scene error[{0}]
500237=coupon unlock scene error[{0}]
500238=coupon project get scene error[found no project by id:{0}]
500239=coupon transfer scene error[{0}]
500240=coupon receive scene error[{0}]
500241=coupon return scene error[{0}]
500242=coupon project list scene error

500300=trade order sync scene error[{0}]
500301=trade refund sync scene error[{0}]
500302=trade order get scene error[{0}]


500400=tag category list scene error[{0}]
500401=tag list scene error[{0}]
500402=tag tagging scene error[{0}]
500403=tag untagging scene error[{0}]
500404=tag user tags scene error[{0}]