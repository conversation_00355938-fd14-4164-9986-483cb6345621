package com.shuyun.fast.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.shuyun.ticket.util.JsonUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import static com.shuyun.fast.base.ApiTags.API_RESP_SUCCESS;

/**
 * @Author: xiaolong.chang
 * @Date: 2018/11/9
 * @Description:
 */
@Data
//@Serdeable
public class ApiResult<T> implements Serializable {

    private static final long serialVersionUID = 444346481744217369L;
    @Schema(title = "是否成功：true 表示成功 false 表示失败")
    @JsonProperty("success")
    private boolean success;
    @Schema(title = "000000 表示成功，其余编码表示具体错误编码")
    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private T data;

    public ApiResult(){

    }

    public ApiResult(boolean success,
                     String code,
                     String message,
                     T data){
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> ApiResult success(String message, T data){
        return new ApiResult(true, API_RESP_SUCCESS, message, data);
    }

    public static <T> ApiResult success(T data){
        return new ApiResult(true, API_RESP_SUCCESS, "成功", data);
    }

    public static ApiResult success(){
        return new ApiResult(true, API_RESP_SUCCESS, "成功", null);
    }

    public static ApiResult failure(String code, String message){
        return new ApiResult(false, code, message, null);
    }
    @Override
    public String toString() {
        return JsonUtil.serialize(this);
    }
}
