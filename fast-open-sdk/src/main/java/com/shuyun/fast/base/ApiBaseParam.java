package com.shuyun.fast.base;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import java.io.Serializable;

@Data
public abstract class ApiBaseParam implements ApiTag, Serializable {

    @Schema(hidden = true, title = "租户id(多租户预留)", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 64, example = "pemybest")
    private String tenantId;
    @Schema(title = "业务代码", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 32, example = "shuyun")
    @NotBlank
    private String bizCode;
    @Schema(title = "请求渠道 POS;WECHAT:微信",
            requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 32, example = "WECHAT")
    @NotBlank
    private String requestChannel;
    @Schema(title = "请求系统 POS;WECHAT_MALL:微信商城;MEMBER_CENTER:会员中心;POINTS_MALL:积分商城",
            requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 32, example = "WECHAT_MALL")
    @NotBlank
    private String requestSystem;
    @Schema(title = "事务ID", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 128, example = "TX2024030490569")
    @NotBlank
    private String transactionId;
}
