package com.shuyun.fast.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class PageResult<T> {
    @Schema(title = "当前页")
    private Integer page;
    @Schema(title = "页大小")
    private Integer pageSize;
    @Schema(title = "总记录数")
    private Long totalCount;
    @Schema(title = "总页码")
    private Integer totalPage;
    @Schema(title = "明细数据")
    private List<T> items;
}
