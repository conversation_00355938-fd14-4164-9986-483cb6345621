package com.shuyun.fast.base;

public class ModelTags {

    public static final String EVENT_FQN_BENEFIT_PROJECT = "event.offer.v3.ProjectNotify";
    public static final String EVENT_TOPIC_ORDER = "fast.event.order";
    public static final String EVENT_TOPIC_REFUND = "fast.event.refund";
    public static final String EVENT_TOPIC_BENEFIT_PROJECT = "fast.event.offer.v3.ProjectNotify";



    public static final String KAFKA_COSMOS_MEMBER_INPUT = "KAFKA_COSMOS_MEMBER_INPUT"; // 会员注册/更新数据
    public static final String KAFKA_COSMOS_GRADE_POINT_INPUT = "KAFKA_COSMOS_GRADE_POINT_INPUT"; // 积分总账/等级变更
    public static final String KAFKA_COSMOS_ACTIVITY_INPUT = "KAFKA_COSMOS_ACTIVITY_INPUT"; // 积分明细
    public static final String KAFKA_COSMOS_WECHATSUBSCRIPTION_INPUT = "KAFKA_COSMOS_WECHATSUBSCRIPTION_INPUT"; // 积分明细
    public static final String KAFKA_COSMOS_WECHATFANS_INPUT = "KAFKA_COSMOS_WECHATFANS_INPUT"; // 粉丝用户
    public static final String KAFKA_COSMOS_WECHATTOAEVENT_INPUT = "KAFKA_COSMOS_WECHATTOAEVENT_INPUT"; // 粉丝消息，粉丝行为
    public static final String KAFKA_COSMOS_CANCEL_INPUT = "KAFKA_COSMOS_CANCEL_INPUT"; // 会员注销，用户注销
    public static final String KAFKA_COSMOS_MASTER_INPUT = "KAFKA_COSMOS_MASTER_INPUT"; // 权益项目。小程序模版
    public static final String KAFKA_COSMOS_LOG_INPUT = "KAFKA_COSMOS_LOG_INPUT"; // 日志消费


    public static final String KAFKA_CRM_LS_MEMBER_INPUT = "KAFKA_CRM_LS_MEMBER_INPUT"; // 罗森会员回调
    public static final String KAFKA_LS_LOG_INPUT = "KAFKA_LS_LOG_INPUT"; // 日志消费

    public static final String DATA_FQN_MDM_SHOP = "data.prctvmkt.%s.Shop";
    public static final String DATA_FQN_MDM_PRODUCT = "data.prctvmkt.%s.Product";
    public static final String DATA_FQN_MDM_ORG = "data.prctvmkt.%s.Org";
    public static final String DATA_FQN_TRADE_MEMBER_ORDER = "data.prctvmkt.%s.Order";
    public static final String DATA_FQN_TRADE_MEMBER_ORDER_PAY = "data.prctvmkt.%s.OrderPay";
    public static final String DATA_FQN_TRADE_MEMBER_ORDER_COUPON = "data.prctvmkt.%s.OrderCoupon";
    public static final String DATA_FQN_TRADE_MEMBER_ORDER_ITEM = "data.prctvmkt.%s.OrderItem";
    public static final String DATA_FQN_TRADE_MEMBER_REFUND = "data.prctvmkt.%s.RefundOrder";
    public static final String DATA_FQN_TRADE_MEMBER_REFUND_ITEM = "data.prctvmkt.%s.RefundOrderItem";
    public static final String DATA_FQN_TRADE_GET = "data.prctvmkt.%s.MainOrder";

    public static final String DATA_FQN_TRADE_CONSUMER_ORDER = "data.prctvmkt.%s.ConsumerOrder";
    public static final String DATA_FQN_TRADE_CONSUMER_ORDER_ITEM = "data.prctvmkt.%s.ConsumerOrderItem";
    public static final String DATA_FQN_TRADE_CONSUMER_REFUND = "data.prctvmkt.%s.ConsumerRefundOrder";
    public static final String DATA_FQN_TRADE_CONSUMER_REFUND_ITEM = "data.prctvmkt.%s.ConsumerRefundOrderItem";

    public static final String DATA_FQN_FAST_BIZCACHE = "data.prctvmkt.fast.BizCache";

    public static final String DATA_FQN_KO_LS_SYNC_LOG = "data.prctvmkt.KO.LsSyncLog";






}
