package com.shuyun.fast.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.base.ApiBaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public abstract class PageParam extends ApiBaseParam {
    @Schema(title = "当前页:从0开始", requiredMode = Schema.RequiredMode.REQUIRED, example = "0", defaultValue = "0")
    private Integer page = 0;
    @Schema(title = "页大小:不超过20", requiredMode = Schema.RequiredMode.REQUIRED, example = "20", defaultValue = "20")
    private Integer pageSize = 20;
    @Schema(title = "开始时间, 格式: yyyy-MM-dd HH:mm:ss ", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @Schema(title = "结束时间  格式: yyyy-MM-dd HH:mm:ss ", example = "2022-12-30T02:24:02.202Z")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    @Schema(title = "排序字段", defaultValue = "CREATED")
    private String sortBy = "CREATED";
    @Schema(title = "排序类型:ASC、DESC,不传默认为DESC", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String sortType = "DESC";
}
