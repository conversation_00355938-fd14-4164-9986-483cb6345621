package com.shuyun.fast.taobao.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 同步响应
 * Created by shuyun on 2016/4/22
 */
@Getter
@Setter
@NoArgsConstructor
@Schema(name = "会员信息同步返回对象")
@Introspected
public class SyncResponse {

    @JsonProperty("is_success")
    @Schema(title = "是否成功", type = "boolean" )
    private boolean isSuccess;//是否成功
    @Schema(title = "明文淘宝昵称", type = "String", example = "王小二")
    private String nick;
    @Schema(title = "明文手机号", type = "String")
    private String mobile;
    private String msg;

    public static SyncResponse fail() {
        SyncResponse response = new SyncResponse();
        response.setSuccess(false);
        return response;
    }

}
