package com.shuyun.fast.taobao.param;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 同步请求
 * Created by s<PERSON><PERSON> on 2016/4/22
 */
@Schema(name = "会员信息同步请求对象")
@Introspected
public class SyncRequest extends TaobaoRequest {
    @Schema(title = "明文淘宝昵称", type = "String", required = true, example = "王小二")
    private String nick;
    @Schema(title = "密文淘宝昵称", type = "String", required = false, example = "afdwe11212saywer")
    @JsonProperty("mix_nick")
    private String mixNick;
    @Schema(title = "有效积分", type = "Long", required = true, example = "200L")
    private Long point;
    @Schema(title = "会员等级", type = "Long", description = "忠诚度的等级,当level为空时，同步淘宝的等级值默认为1", example = "1L")
    private Long level;
    @Schema(title = "成长值", type = "Long", required = true, example = "200L")
    private Long levelPoint;
    private String extend;
    private Long version;
    private String timestamp;
    private String sign;
    private String msg;

    @Override
    public String toString() {
        return "SyncRequest{" +
                "nick='" + nick + '\'' +
                ", mixNick='" + mixNick + '\'' +
                ", point=" + point +
                ", level=" + level +
                ", extend='" + extend + '\'' +
                ", version=" + version +
                ", timestamp='" + timestamp + '\'' +
                ", sign='" + sign + '\'' +
                ", msg='" + msg + '\'' +
                '}';
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public String getMixNick() {
        return mixNick;
    }

    public void setMixNick(String mixNick) {
        this.mixNick = mixNick;
    }

    public Long getPoint() {
        return point;
    }

    public void setPoint(Long point) {
        this.point = point;
    }

    public Long getLevel() {
        return level;
    }

    public void setLevel(Long level) {
        this.level = level;
    }

    public Long getLevelPoint() {
        return levelPoint;
    }

    public void setLevelPoint(Long levelPoint) {
        this.levelPoint = levelPoint;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
