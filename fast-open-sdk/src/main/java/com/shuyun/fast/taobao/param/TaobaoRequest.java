package com.shuyun.fast.taobao.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * 淘宝请求抽象
 * Created by shuyun on 2016/4/18.
 */
@NoArgsConstructor
@ToString
@Introspected
public class TaobaoRequest implements Serializable{

    @Schema(title = "卖家昵称(店铺名称)", type = "String", required = true,example = "王小二店铺")
    @JsonProperty("seller_name")
    private String sellerName;   //卖家 昵称

    @Schema(title = "混淆手机号", type = "String",  example = "afd23r2qrafdtry")
    @JsonProperty("mix_mobile")
    private String mixMobile;   //加密后的手机号码

    // 由于sync 同步积分给淘宝时，可能没有mobile
    @Schema(title = "明文手机号.(绑定查询请求中没有此字段,只有在查询时此字段可能有值)", type = "String",  example = "1333668888")
    private String mobile;  //明文手机号码

    @Schema(title = "淘宝平台ouid" , type = "String" , example = "3452435234523")
    private String ouid;

    @Schema(title = "淘宝平台omid" , type = "String", example = "7428434234234")
    private String omid;


    //验证参数
    public  boolean vaidatePrame(){

        return true;
    }

    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public String getMixMobile() {
        return mixMobile;
    }

    public void setMixMobile(String mixMobile) {
        this.mixMobile = mixMobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOuid() {
        return ouid;
    }

    public void setOuid(String ouid) {
        this.ouid = ouid;
    }

    public String getOmid() {
        return omid;
    }

    public void setOmid(String omid) {
        this.omid = omid;
    }
}
