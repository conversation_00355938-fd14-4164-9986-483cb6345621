package com.shuyun.fast.ls.param;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(name = "ls会员回调同步请求对象")
@Introspected
public class SyncMemberRequest {
    public String organizationId;
    public String orgUserId;
    public String userId;
}