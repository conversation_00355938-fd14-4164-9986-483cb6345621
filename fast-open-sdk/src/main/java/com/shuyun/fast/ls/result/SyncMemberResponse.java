package com.shuyun.fast.ls.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(name = "ls会员回调返回参数")
@Introspected
public class SyncMemberResponse {
    @JsonProperty("sysDatetime")
    private Long sysDatetime;
    private LsMemberInfo data;
    private String code;
    private String message;

    @Getter
    @Setter
    public static class LsMemberInfo {
        @JsonProperty("isFirstBinding")
        private boolean firstBinding;
    }
}