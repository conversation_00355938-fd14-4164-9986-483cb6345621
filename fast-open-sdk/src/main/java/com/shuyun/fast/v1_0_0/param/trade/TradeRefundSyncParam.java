package com.shuyun.fast.v1_0_0.param.trade;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.domain.TradeRefund;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class TradeRefundSyncParam extends ApiBaseParam {
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "交易退单对象", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private TradeRefund refund;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_TRADE_REFUND_SYNC;
    }
}
