package com.shuyun.fast.v1_0_0.param.grade;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class GradeMetadataParam extends ApiBaseParam {
    @Schema(title = "等级业务类型 GRADE-等级 MEDAL-勋章 不传默认为: GRADE", defaultValue = "GRADE")
    private String gradeBizType = "GRADE";//GRADE-等级 MEDAL-勋章
    @Schema(title = "是否需要规则信息", defaultValue = "false")
    private Boolean rulesRequired = false;
    @Override
    public String apiName() {
        return ApiTags.API_NAME_GRADE_METADATA_GET;
    }
}
