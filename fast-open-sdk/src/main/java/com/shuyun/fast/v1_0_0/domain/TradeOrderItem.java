package com.shuyun.fast.v1_0_0.domain;


import com.fasterxml.jackson.annotation.*;
import com.shuyun.fast.base.ModelTag;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.v1_0_0.constant.OrderOwnerType;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@JsonFilter("tradeOrderItemFilter")
public class TradeOrderItem implements Serializable, ModelTag {
    @Schema(hidden = true, title="会员类型")
    private String memberType;
    @Schema(hidden = true, title = "订单归属类型 consumer-消费者  member-会员")
    @NotNull
    @JsonIgnore
    private String orderOwnerType;
    @Schema(hidden = true, title = "对象id")
    private String id;
    @Schema(title = "消费门店类型code", example = "TAOBAO")
    private String shopTypeCode;
    @Schema(hidden = true, title = "消费门店code", requiredMode = Schema.RequiredMode.REQUIRED, example = "SHOP001")
    private String shopCode;
    @Schema(hidden = true, title = "消费门店名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "SHOP001")
    private String shopName;
    @Schema(hidden = true, title = "渠道", example = "TAOBAO")
    private String channelType;
    @NotEmpty
    @Schema(title = "订单类型:正常单-NORMAL,赠品订单-GIFT。", requiredMode = Schema.RequiredMode.REQUIRED, example = "NORMAL")
    private String orderType;
    @NotNull
    @Schema(title = "应付总金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "85")
    private Double totalFee;
    @NotNull
    @Schema(title = "实付总金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "85")
    private Double payment;
    @Schema(title = "折扣率 <=1", example = "0.85")
    private Double discountRate;
    @Schema(title = "折扣金额", example = "15")
    private Double discountFee;
    @Schema(title = "8时区订单完成时间", description = "格式: yyyy-MM-dd HH:mm:ss 订单完成场景必填", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;
    @Schema(title = "8时区下单时间", description = "格式: yyyy-MM-dd HH:mm:ss", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;
    @Schema(title = "8时区订单付款时间", description = "格式: yyyy-MM-dd HH:mm:ss 付款场景必填", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;
    @Schema(title = "子订单扩展字段", example = "{\"string\":\"update\"}")
    private Map<String, Object> extension = new HashMap<String, Object>();
    @JsonAnySetter
    public void setExtension(String key, Object value) {
        this.extension.put(key, value);
    }
    //enabled = true开启后本方法将卡券自定义属性动态扩展到本对象最外层,如果想统一放到extData对象则请设置为false
    @JsonAnyGetter(enabled = true)
    public Map<String, Object> getExtension() {
        return this.extension;
    }
    @NotEmpty
    @Schema(title = "子订单状态:CREATED-下单,CANCELLED-订单取消,DELIVERED-已发货,CONFIRMED-卖家已确认,FINISHED-完成.更多请去字典表配置", requiredMode = Schema.RequiredMode.REQUIRED, example = "FINISHED")
    private String status;
    @Schema(hidden = true, title = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "087989aaf6af4e58897794d18axxxxx")
    private String orderId;
    @Schema(title = "子订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "bf3a654f1afa4859bb5c0e8846bxxxxx")
    private String orderItemId;
    @NotEmpty
    @Schema(title = "商品Code", requiredMode = Schema.RequiredMode.REQUIRED, example = "P001")
    private String productCode;
    @NotEmpty
    @Schema(title = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "P001")
    private String productName;
    @NotNull
    @Schema(title = "商品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer quantity;
    @Schema(title = "吊牌价", example = "100")
    private Double tagPrice;
    @Schema(title = "零售价", example = "100")
    private Double retailPrice;
    @Schema(title = "商品批次编码", example = "S001")
    private String skuId;
    @Schema(title = "商品图片url", example = "[\"https://allpro.saas.top/FsB-uzzX4WtCzpDu-Vuwqp2OdNbQ?e=1547742055&token=yNjhOp7gnH4St9yP72OlwuQ6JUNQf49pxAuGWYFt:1eZIUjRKIekGuMGG-c-s7UqNF1Y=;\"]")
    private List<String> picture;
    @Schema(hidden = true, title = "8时区最后更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSync = LocalDateTime.now();

    @Override
    public String fqn() {
        if(orderOwnerType.equals(OrderOwnerType.MEMBER)){
            return ModelTags.DATA_FQN_TRADE_MEMBER_ORDER_ITEM;
        }
        return ModelTags.DATA_FQN_TRADE_CONSUMER_ORDER_ITEM;
    }
}
