package com.shuyun.fast.v1_0_0.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class GradeBudgetDifference {
    @Schema(title = "属性code", example = "yearTotalPayAmount")
    private String attributeCode;
    @Schema(title = "属性名称", example = "一年内累计实付金额")
    private String attributeName;
    @Schema(title = "属性累计值", example = "500")
    private Long attributeValue;
    @Schema(title = "等级阈值", example = "1000")
    private Long gradeThreshold;
    @Schema(title = "距离下一等级差值", example = "500")
    private Long difference;
}
