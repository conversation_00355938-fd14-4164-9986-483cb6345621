package com.shuyun.fast.v1_0_0.param.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.shuyun.ticket.benefit.vo.Order;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class CouponAvailableListParam extends CouponBaseParam {
//    @Schema(hidden = true, title = "校验规则或保存实例扩展字段", description = "对应卡券方案-规则-数据配置中的校验数据字段名。依规则配置确定是否对传入字段进行校验，数据会被保存到实例模型")
//    private Map<String, ?> extension;
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "是否展示项目信息", description = "为false的话响应参数中没有project")
    private Boolean showProject = false;
    @Schema(title = "根据子订单判断实例是否可用", description = "默认为false,如果传入true会返回实例可用的子订单ID列表", defaultValue = "false")
    private Boolean checkByOrderItem = false;
    @Schema(title = "订单信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private Order order;
    @Schema(title = "是否展示选择器数据:默认false", description = "为false的话响应参数不返回任何选择器数据")
    private Boolean showSelectorData = false;
    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_AVAILABLE_LIST;
    }
}
