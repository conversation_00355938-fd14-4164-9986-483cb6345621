package com.shuyun.fast.v1_0_0.result;

import com.shuyun.ticket.benefit.vo.BenefitDiscountResult;
import com.shuyun.ticket.benefit.vo.Order;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class CouponDiscountCalcResult {
//    @Schema(title = "扩展信息")
//    private Map<String, Object> extension;
    @Schema(title = "订单信息")
    private Order order;
    @Schema(title = "权益实例优惠结果列表")
    private List<BenefitDiscountResult> discountResults;
}
