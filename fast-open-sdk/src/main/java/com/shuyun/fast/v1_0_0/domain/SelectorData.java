package com.shuyun.fast.v1_0_0.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class SelectorData {
    @Schema(title = "核销限制:指定店铺集合-选择器数据")
    private List<String> includeShops;
    @Schema(title = "核销限制:排除店铺集合-选择器数据")
    private List<String> excludeShops;
    @Schema(title = "核销限制:存在商品属于商品集合-选择器数据  订单内存在一件商品属于指定范围，卡券即可用")
    private List<String> includeGoods;
    @Schema(title = "核销限制:所有商品不属于商品集合-选择器数据  订单内所有商品不属于指定范围，卡券即可用")
    private List<String> excludeGoods;
    @Schema(title = "核销限制:所有商品属于商品集合-选择器数据  订单内所有商品属于指定范围，卡券即可用")
    private List<String> allIncludeGoods;
    @Schema(title = "核销限制:存在商品不属于商品集合-选择器数据 订单内存在一件商品不属于指定范围，卡券即可用")
    private List<String> anyExcludeGoods;
}
