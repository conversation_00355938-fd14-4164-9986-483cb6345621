package com.shuyun.fast.v1_0_0.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class Grade extends MemberId {
    @Schema(title = "等级ID")
    private Long id;
    @Schema(title = "等级名称")
    private String name;
    @Schema(title = "8时区等级生效时间  ,格式为:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectTime;
    @Schema(title = "8时区等级过期时间  ,格式为:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiredTime;
}
