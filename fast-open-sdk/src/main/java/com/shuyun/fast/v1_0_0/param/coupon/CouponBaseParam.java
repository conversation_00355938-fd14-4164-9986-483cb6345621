package com.shuyun.fast.v1_0_0.param.coupon;

import com.shuyun.fast.base.ApiBaseParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public abstract class CouponBaseParam extends ApiBaseParam {
    @Schema(hidden = true, title = "是否保证原子性")
    private Boolean atomic = true;
    @Schema(hidden = true, title = "校验规则或保存实例扩展字段", description = "对应卡券方案-规则-数据配置中的校验数据字段名。依规则配置确定是否对传入字段进行校验，数据会被保存到实例模型")
    private Map<String, Object> extension;
}
