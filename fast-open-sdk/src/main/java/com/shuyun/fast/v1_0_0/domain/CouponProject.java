package com.shuyun.fast.v1_0_0.domain;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.air.framework.domain.BeanStatus;
import com.shuyun.ticket.base.domain.*;
import com.shuyun.ticket.benefit.domain.*;
import com.shuyun.ticket.benefit.domain.thirdParty.WeChatPayCouponStocks;
import com.shuyun.ticket.benefit.enums.weimob.CouponCostType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.shuyun.ticket.benefit.enums.CombinationType;
import java.math.BigDecimal;

@Data
public class CouponProject extends SelectorData implements Serializable {

    @Data
    @Schema(title = "权益范围")
    public static class Restrict{
        @Schema(type = "string", title = "权益范围:店铺类型", description = "UNRESTRICT:不限;INCLUDE:指定店铺;EXCLUDE:排除店铺")
        private ShopRestrictType shopsRefType;
        @Schema(title = "权益范围:指定店铺")
        private String shopsRef;
        @Schema(title = "权益范围:指定平台")
        private String platformsRef;
        @Schema(type = "string", title = "权益范围:商品类型", description = "UNRESTRICT:不限;INCLUDE:指定商品;EXCLUDE:排除商品")
        private GoodsRestrictType goodsRefType;
        @Schema(title = "权益范围:指定商品")
        private String goodsRef;
    }

    @Data
    @Schema(title = "发放限制")
    public static class GrantRestrict{
        @Schema(title = "发放限制:开始时间限制", description = "8时区格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startAt;
        @Schema(title = "发放限制:结束时间限制", description = "8时区格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endAt;
        @Schema(title = "发放限制:指定平台")
        private String platformsRef;
        @Schema(title = "发放限制:调用次数限制")
        private Integer invokeNum;
        @Schema(title = "发放限制:单人单日调用次数限制")
        private Integer invokeNumByUserPerDay;
        @Schema(title = "发放限制:单日调用次数限制")
        private Integer invokeNumByPerDay;
    }

    @Data
    @Schema(title = "核销限制")
    public static class UseRestrict{
        @Schema(type = "string", title = "核销限制:店铺类型", description = "UNRESTRICT:不限;INCLUDE:指定店铺;EXCLUDE:排除店铺")
        private ShopRestrictType shopsRefType;
        @Schema(hidden = true, title = "核销限制:指定店铺-选择器id")
        private String shopsRef;
        @Schema(title = "核销限制:指定平台")
        private String platformsRef;
        @Schema(type = "string", title = "核销限制:商品类型", description = "UNRESTRICT:不限;INCLUDE:指定商品;EXCLUDE:排除商品")
        private GoodsRestrictType goodsRefType;
        @Schema(hidden = true, title = "核销限制:指定商品-选择器id")
        private String goodsRef;
        @Schema(title = "核销限制:开始时间限制", description = "8时区格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startAt;
        @Schema(title = "核销限制:结束时间限制", description = "8时区格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endAt;
        @Schema(title = "核销限制:单人单日调用次数限制")
        private Integer invokeNumByUserPerDay;
        @Schema(hidden = true, title = "核销限制:相对时限", description = "目前仅老卡券迁移新卡券使用到了该属性")
        private TimePeriod wait;
        @Schema(type = "string", title = "时间类型")
        private TimeType timeType;
        @Schema(title = "周期")
        private TimeCycle timeCycle;
    }

    @Data
    @Schema(title = "反核销限制")
    public static class CancelUseRestrict{
        @Schema(title = "反核销限制:调用次数限制")
        private Integer invokeNum;
        @Schema(title = "反核销限制:单人单日调用次数限制")
        private Integer invokeNumByUserPerDay;
        @Schema(title = "反核销限制:开始时间限制", description = "8时区格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startAt;
        @Schema(title = "反核销限制:结束时间限制", description = "8时区格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endAt;
    }

    @Data
    @Schema(title = "优惠")
    public static class Discount{
        @Schema(type = "string", title = "优惠:优惠类型", description = "RATIO_OFF:折扣;AMOUNT_OFF:抵扣,EXCHANGE:兑换")
        private DiscountMode mode;
        @Schema(type = "string", title = "优惠:优惠范围", description = "FOR_GOODS:商品优惠;FOR_SHOP:店铺优惠;GENERAL:通用优惠")
        private DiscountScope scope;
        @Schema(title = "优惠:优惠面额")
        private Denomination amount;
        @Schema(title = "优惠:优惠金额下限")
        private BigDecimal minAmount;
        @Schema(title = "优惠:优惠金额上限")
        private BigDecimal maxAmount;
        @Schema(title = "优惠:最大优惠商品数量")
        private Integer maxNum;
    }
    @Data
    @Schema(title = "兑换")
    public static class Exchange{
        @Schema(title = "兑换:指定兑换商品")
        private String goods;
        @Schema(title = "兑换:兑换数量")
        private Integer num;
        @Schema(title = "兑换:每种商品最多兑换X件")
        private Integer maxPerGoods;
    }
    @Data
    @Schema(title = "时间长度")
    public static class TimePeriod{
        @Schema(title = "年")
        private Integer year;
        @Schema(title = "季度")
        private Integer quarter;
        @Schema(title = "月")
        private Integer month;
        @Schema(title = "周")
        private Integer week;
        @Schema(title = "日")
        private Integer day;
        @Schema(title = "时")
        private Integer hour;
        @Schema(title = "分")
        private Integer minute;
        @Schema(title = "秒")
        private Integer second;
    }
    @Data
    @Schema(title = "生效 有效期")
    public static class EffectivePeriod{
        @Schema(type = "string", title = "有效期类型", description = "NEVER:永不,FIXED_TIME:绝对时间,RELATIVE_TIME:相对时间,NATURAL_PERIOD:自然时长,CYCLE:周期")
        private EffectiveType type;
        @Schema(title = "绝对时间", description = "时间格式是8时区:yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime fixed;
        @Schema(title = "相对时间")
        private List<RelativeTime> relatives;
    }
    @Data
    @Schema(title = "生效限制")
    public static class EffectRestrict{
        @Schema(title = "生效限制:开始时间限制", description = "8时区格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startAt;
        @Schema(title = "生效限制:结束时间限制", description = "8时区格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endAt;
    }

    @Data
    @Schema(title = "失效限制")
    public static class ExpireRestrict{
        @Schema(title = "失效限制:开始时间限制", description = "8时区格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startAt;
        @Schema(title = "失效限制:结束时间限制", description = "8时区格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endAt;
    }

    @Data
    @Schema(title = "赠送限制")
    public static class TransferRestrict{
        @Schema(title = "赠送限制:调用次数限制,每张卡券最多赠送*次")
        private Integer invokeNum;
        @Schema(title = "赠送限制:开始时间限制", description = "8时区格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startAt;
        @Schema(title = "赠送限制:结束时间限制", description = "8时区格式:yyyy-MM-dd HH:mm:ss，例如:2022-10-21 00:01:01")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endAt;
    }
    @Data
    @Schema(title = "赠送有效期")
    public static class TransferPeriod{
        @Schema(type = "string", title = "有效期类型", description = "RELATIVE_TIME:相对时间")
        private EffectiveType type;
        @Schema(title = "相对时间", description = "多少*内未接收，自动退回")
        private List<RelativeTime> relatives;
    }
    @Data
    @Schema(title = "成本")
    public static class CouponCost{
        @Schema(type = "string", title = "成本类型", description = "FIXED:固定、PERCENTAGE:百分比")
        private CouponCostType couponCostType;
        @Schema(title = "成本金额&比例")
        private BigDecimal couponCost;
        @Schema(title = "组织架构分摊")
        private OrgShare orgShare;
    }

    @Data
    @Schema(title = "微盟卡券", description = "微盟平台卡券专用字段")
    public static class WeiMob{
        @Schema(title = "是否开启客户直接领取发券")
        private Boolean customerDirectReceive;
        @Schema(title = "活动发券")
        private Boolean activityPublish;
        @Schema(title = "是否开启企微助手渠道")
        private Boolean enterpriseAssistant;
        @Schema(title = "是否开启商家发券渠道")
        private Boolean merchantPublish;
        @Schema(title = "是否开启导购发券")
        private Boolean shoppingPublish;
        @Schema(title = "是否开启客户列表发券")
        private Boolean customerListPublish;
        @Schema(title = "是否开启客服发券")
        private Boolean servicePublish;
        @Schema(title = "是否可以分享领取")
        private Boolean canShare;
        @Schema(title = "是否允许下级投放")
        private Boolean canStoreLaunch;
        @Schema(title = "是否包含自建商品")
        private Boolean includeStoreGoods;
        @Schema(title = "是否是全部核销场景")
        private Boolean isAllUseScene;
        @Schema(title = "核销场景", description = "字符串以逗号隔开的格式")
        private String shoppingMallSceneList;
        @Schema(title = "是否与其他活动共享优惠")
        private Boolean canUseWithOtherDiscount;
        @Schema(title = "商城可共享优惠", description = "字符串以逗号隔开的格式")
        private String shoppingMallDiscount;
    }
    @Data
    @Schema(title = "付费权益")
    public static class PayBenefit{
        @Schema(title = "是否付费")
        private Boolean isPayment;
    }

    @Data
    @Schema(title = "图片")
    public static class MaterialImage{
        @Schema(title = "卡券列表图片")
        private String cardVoucherListImage;
        @Schema(title = "卡券详情图片")
        private String cardVoucherDetailImage;
        @Schema(title = "品牌logo")
        private String brandLogo;
        @Schema(title = "商品图片")
        private String goodsImage;
    }

    @Schema(title = "项目id")
    private String id;
    @Schema(hidden = true, title = "卡券项目分支的根版本,此字段作项目ID，本字段对外隐藏,老卡券（2.0使用该字段）,新版本卡券3.0使用ID字段")
    private String branch;
    @Schema(title = "项目名称")
    private String title;
    @Schema(title = "业务类型id")
    private String kind;
    @Schema(title = "活动名称")
    private String campaignTitle;
    @Schema(title = "描述信息")
    private String description;
    @Schema(type = "string", title = "状态", description = "DRAFT:草稿;RELEASING:发布中;RELEASED:已发布;OFFLINE:已下线;DELETED:已删除;ARCHIVED:已归档;SUBMITTED:审批中;APPROVED:已通过;REJECTED:已拒绝")
    private BeanStatus status;
    @Schema(title = "权益范围")
    private Restrict restrict;
    @Schema(title = "发放限制")
    private GrantRestrict grantRestrict;
    @Schema(title = "核销限制")
    private UseRestrict useRestrict;
    @Schema(title = "反核销限制")
    private CancelUseRestrict cancelUseRestrict;
    @Schema(title = "8时区项目记录更新时间", description = "时间格式是零时区:yyyy-MM-dd'T'HH:mm:ss'Z'")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateAt;
    @Schema(title = "发放总量")
    private Long maxQuantity;
    @Schema(title = "可用次数限制")
    private Long useCountRestrict;
    @Schema(title = "是否允许转赠")
    private Boolean transferable;
    @Schema(type = "string", title = "权益类型", description = "DISCOUNT:优惠券; EXCHANGE:兑换券")
    private BenefitType type;
    @Schema(title = "优惠")
    private Discount discount;
    @Schema(title = "兑换")
    private Exchange exchange;
    @Schema(title = "最大叠加张数")
    private Integer maxOverlayNum;
    @Schema(title = "可否组合使用")
    private Boolean combinationEnabled;
    @Schema(title = "已发放数量")
    private Long grantedCount;
    @Schema(title = "已核销次数")
    private Long usedCount;
    @Schema(title = "是否过期提醒")
    private Boolean reminderBeforeExpirationEnable;
    @Schema(title = "时间间隔")
    private TimePeriod reminderBeforeExpiration;
    @Schema(title = "生效有效期")
    private EffectivePeriod effectPeriod;
    @Schema(title = "失效有效期")
    private EffectivePeriod expiredPeriod;
    @Schema(title = "卡券项目扩展属性")
    private Map<String, Object> extData = new HashMap<String, Object>();
    @JsonAnySetter
    public void setExtData(String key, Object value) {
        this.extData.put(key, value);
    }
    //enabled = true开启后本方法将卡券自定义属性动态扩展到本对象最外层,如果想统一放到extData对象则请设置为false
    @JsonAnyGetter(enabled = true)
    public Map<String, Object> getExtData() {
        return this.extData;
    }

    @Schema(title = "模版ID")
    private String template;
    @Schema(title = "编码生成器ID")
    private String codeMaker;
    @Schema(title = "生效限制")
    private EffectRestrict effectRestrict;
    @Schema(title = "失效限制")
    private ExpireRestrict expireRestrict;
    @Schema(title = "父版本")
    private String previous;
    @Schema(title = "方案")
    private String program;
    @Schema(title = "子订单是否参与优惠分摊")
    private Boolean subOrderShareDiscount;
    @Schema(type = "string", title = "叠加类型 NO_OVERLAY:不叠加 、SINGLE:单项目叠加 、ACROSS:跨项目叠加")
    private CombinationType combinationType;
    @Schema(title = "是否允许店铺直接发放", description = "三方平台对接字段,开启店铺领取优惠券")
    private Boolean storeDirectDistribution;
    @Schema(title = "赠送限制")
    private TransferRestrict transferRestrict;
    @Schema(title = "赠送有效期")
    private TransferPeriod transferPeriod;
    @Schema(title = "微盟卡券专用对象")
    private WeiMob weiMob;
    @Schema(title = "微信支付代金券")
    private WeChatPayCouponStocks weChatPayCouponStocks;
    @Schema(title = "成本")
    private CouponCost couponCost;
    @Schema(title = "是否仅限正价商品可用")
    private Boolean positivePriceGoods;
    @Schema(title = "付费权益")
    private PayBenefit payBenefit;
    @Schema(title = "单店最多发放")
    private Integer storeLimitNum;
    @Schema(title = "下单可同时使用抵扣", description = "连接的字符串数组")
    private String orderDeductList;
    @Schema(title = "图片")
    private MaterialImage materialImage;
    @Schema(title = "是否发布后立即创建", description = "是：项目发布后立即创建卡券实例，应用于匿名券、实体券；否：支持接口发放或者人工发放，应用于人群营销发放、事件营销发放、人工发放。")
    private Boolean createNow;
    @Schema(title = "是否创建和发放后立即启用", description = "是：创建和发放后立即启用；否：支持接口启用或者人工启用")
    private Boolean activateNow;
    @Schema(title = "主体ID")
    private String subject;

}
