package com.shuyun.fast.v1_0_0.param.grade;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class GradeBudgetParam extends ApiBaseParam {

    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "等级业务类型,不传,默认为: GRADE", defaultValue = "GRADE")
    private String gradeBizType = "GRADE";
    @Schema(title = "目标等级id", requiredMode = Schema.RequiredMode.REQUIRED, example = "60006")
    @NotBlank
    private String gradeId;
    @Override
    public String apiName() {
        return ApiTags.API_NAME_GRADE_BUDGET;
    }
}
