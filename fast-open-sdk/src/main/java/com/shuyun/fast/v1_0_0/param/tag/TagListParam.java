package com.shuyun.fast.v1_0_0.param.tag;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class TagListParam extends ApiBaseParam {

    @Schema(title = "标签目录id集合(调用前请去重)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private Set<Long> categoryIds;
    @Schema(title = "当前页:从0开始", requiredMode = Schema.RequiredMode.REQUIRED, example = "0", defaultValue = "0")
    @NotNull
    @Min(0)
    private Integer page = 0;
    @Schema(title = "页大小:不超过20", requiredMode = Schema.RequiredMode.REQUIRED, example = "20", defaultValue = "20")
    @NotNull
    @Max(20)
    private Integer pageSize = 20;
    @Override
    public String apiName() {
        return ApiTags.API_NAME_TAG_LIST;
    }
}
