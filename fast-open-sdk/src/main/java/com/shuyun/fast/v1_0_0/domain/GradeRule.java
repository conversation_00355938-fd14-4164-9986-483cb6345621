package com.shuyun.fast.v1_0_0.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class GradeRule {

    @Schema(title = "属性code", example = "yearTotalPayAmount")
    private String attributeCode;
    @Schema(title = "属性名称", example = "一年内累计实付金额")
    private String attributeName;
    @Schema(title = "比较运算符", example = ">=")
    private String comparisonOperator;
    @Schema(title = "当前等级阈值", example = "500")
    private Long gradeThreshold;
    @Schema(title = "下一等级预置", example = "1000")
    private Long nextGradeThreshold;
}
