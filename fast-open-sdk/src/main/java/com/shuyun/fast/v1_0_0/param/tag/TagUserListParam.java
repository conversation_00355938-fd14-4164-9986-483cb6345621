package com.shuyun.fast.v1_0_0.param.tag;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class TagUserListParam extends ApiBaseParam {

    @Override
    public String apiName() {
        return ApiTags.API_NAME_TAG_USER_LIST;
    }
}
