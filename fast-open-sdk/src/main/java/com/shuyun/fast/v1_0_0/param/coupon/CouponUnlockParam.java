package com.shuyun.fast.v1_0_0.param.coupon;

import com.shuyun.fast.base.ApiTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class CouponUnlockParam extends CouponLockParam {
    @Schema(title = "锁定事务ID,锁定时使用的事务ID,解锁时必须传送", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 128)
    private String lockTransactionId;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_UNLOCK;
    }
}
