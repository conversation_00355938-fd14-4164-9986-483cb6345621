package com.shuyun.fast.v1_0_0.param.checklist;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RiskControlChecklistAddRequest {
    @Schema(title = "黑名单类型 BLACK:客户 MOBILE:手机 EMAIL:邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checklistType;
    @Schema(title = "fqn 黑名单所属模型,客户黑名单必填,其他类型此参数无效")
    private String fqn;
    @Schema(title = "客户唯一标识,根据类型来定BLACK:客户id MOBILE:手机号 EMAIL:邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customer;
}
