package com.shuyun.fast.v1_0_0.param.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class MemberMobileModifyParam extends ApiBaseParam {
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String mobile;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_MOBILE_MODIFY;
    }
}
