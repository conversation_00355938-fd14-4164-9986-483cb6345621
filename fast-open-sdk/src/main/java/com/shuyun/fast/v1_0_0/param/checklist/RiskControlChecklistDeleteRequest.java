package com.shuyun.fast.v1_0_0.param.checklist;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RiskControlChecklistDeleteRequest {
    @Schema(title = "黑名单类型 BLACK:客户 MOBILE:手机 EMAIL:邮箱")
    private String checklistType;
    @Schema(title = "fqn 黑名单所属模型,客户黑名单必填,其他类型此参数无效")
    private String fqn;
    @Schema(title = "fqn 黑名单所属模型,客户黑名单必填,其他类型此参数无效")
    private String customer;
//    @Schema(title = "分组id，非必填")
//    private String groupId;
//    @Schema(title = "备注")
//    private String remark;
}
