package com.shuyun.fast.v1_0_0.param.point;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class PointRevertParam extends ApiBaseParam {
    @Schema(title = "待撤销的原交易事务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "TX2024030490569")
    @NotBlank
    private String originalTransactionId;
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "积分业务类型,不传,默认为: POINT", defaultValue = "POINT")
    private String pointBizType = "POINT";
    @Schema(title = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "备注")
    @NotEmpty
    private String description;
    @Schema(title = "店铺code")
    private String shopCode;
    @Schema(hidden = true, title = "积分变更方式，需在忠诚度字典表中预设，若传入忠诚度不存在的值会出现编码转换异常")
    private String changeMode;
    @Schema(title = "活动名称")
    private String actionName;
    @Schema(title = "变更类型: SEND-发放积分  DEDUCT-消耗积分 USE_FREEZE-消耗已冻结", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String revertType;
    @Schema(title = "积分值",  requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private Integer point;
    @Schema(title = "扩展字段一")
    private String KZZD1;
    @Schema(title = "扩展字段二")
    private String KZZD2;
    @Schema(title = "扩展字段三")
    private String KZZD3;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_POINT_REVERT;
    }
}
