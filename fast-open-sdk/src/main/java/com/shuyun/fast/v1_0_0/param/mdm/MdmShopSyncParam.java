package com.shuyun.fast.v1_0_0.param.mdm;

import com.fasterxml.jackson.annotation.*;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.ModelTag;
import com.shuyun.fast.base.ModelTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
@JsonFilter("mdmShopSyncParamFilter")
public class MdmShopSyncParam extends ApiBaseParam implements ModelTag {
    @Schema(hidden = true, title="会员类型")
    private String memberType;
    @Schema(hidden = true, title = "对象id")
    private String id;
    @NotEmpty
    @Schema(title = "门店编码", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 64, example = "SHOP001")
    private String shopCode;
    @NotEmpty
    @Schema(title = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 255, example = "SHOP001")
    private String shopName;
    @Schema(title = "门店类型code. 在字段表维护", example = "TAOBAO")
    private String shopTypeCode;
    @Schema(title = "店铺类型名称", example = "淘宝")
    private String shopType;
    @Schema(title = "省code", example = "shanghai")
    private String provinceCode;
    @Schema(title = "省份名称", example = "上海")
    private String provinceName;
    @Schema(title = "市code", example = "shanghai")
    private String cityCode;
    @Schema(title = "城市名称", example = "上海")
    private String cityName;
    @Schema(title = "区/县code", example = "pudong")
    private String districtCode;
    @Schema(title = "区/县名称", example = "浦东新区")
    private String districtName;
    @Schema(title = "地址", example = "南京东路100号")
    private String address;
    @Schema(title = "商圈code", example = "pudong")
    private String tzCode;
    @Schema(title = "商圈名称", example = "浦东新区")
    private String tzName;
    @Schema(title = "联系电话", example = "18900000000")
    private String contactTel;
    @Schema(title = "营业状态", example = "OPENING")
    private String status;
    @Schema(title = "门店定位经度", example = "121.346301")
    private String longitude;
    @Schema(title = "门店定位纬度", example = "31.319295")
    private String latitude;
    @Schema(title = "门店图片url", example = "[\"http://www.baidu.com\"]")
    private List<String> picture;
    @Schema(title = "门店介绍", example = "东方明珠")
    private String introduction;
    @Schema(title = "8时区开店时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime openTime;
    @Schema(title = "8时区关店时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime closeTime;
    @Schema(title = "绑定部门ID", example = "001")
    private String bindDepartmentId;
    @Schema(title = "门店归属组织Code", example = "1")
    private String orgCode;
    @Schema(title = "门店归属组织Id", example = "1")
    private Long orgId;
    @Schema(hidden = true, title = "渠道类型", example = "TAOBAO")
    private String channelType;
    @Schema(hidden = true, title = "是否有效Y/N(店仓状态)", example = "Y")
    private String isValid = "Y";
    @Schema(hidden = true, title = "8时区最后更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSync = LocalDateTime.now();
    @Schema(title="扩展字段", example = "{\"string\":\"update\"}")
    private Map<String, Object> extension = new HashMap<String, Object>();
    @JsonAnySetter
    public void setExtension(String key, Object value) {
        this.extension.put(key, value);
    }
    //enabled = true开启后本方法将卡券自定义属性动态扩展到本对象最外层,如果想统一放到extData对象则请设置为false
    @JsonAnyGetter(enabled = true)
    public Map<String, Object> getExtension() {
        return this.extension;
    }
    @Override
    public String apiName() {
        return ApiTags.API_NAME_MDM_SHOP_SYNC;
    }

    @Override
    public String fqn() {
        return ModelTags.DATA_FQN_MDM_SHOP;
    }
}
