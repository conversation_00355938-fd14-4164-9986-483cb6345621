package com.shuyun.fast.v1_0_0.param.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class MemberDeregisterParam extends ApiBaseParam {
    @Schema(title = "会员识别对象(memberId与userId二者仅可选其一且memberId必输)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "注销时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deregisterTime;
//    private String channelType;
    @Schema(title = "店铺code")
    private String shopCode;
    @Schema(title = "操作人", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String operator;
    @Schema(title = "备注", example = "恶意刷单", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String remark;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_DEREGISTER;
    }
}
