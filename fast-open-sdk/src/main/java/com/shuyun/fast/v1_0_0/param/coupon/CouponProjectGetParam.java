package com.shuyun.fast.v1_0_0.param.coupon;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotEmpty;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class CouponProjectGetParam extends ApiBaseParam {

    @Schema(title = "项目id", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 32)
    @NotEmpty
    private String projectId;
    @Schema(title = "是否从缓存读取", defaultValue = "true")
    private Boolean useCache = true;
    @Schema(title = "是否展示选择器数据:默认false", description = "为false的话响应参数不返回任何选择器数据")
    private Boolean showSelectorData = false;
    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_PROJECT_GET;
    }
}
