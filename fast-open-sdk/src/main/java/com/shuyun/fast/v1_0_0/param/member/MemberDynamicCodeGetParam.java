package com.shuyun.fast.v1_0_0.param.member;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class MemberDynamicCodeGetParam extends ApiBaseParam {
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "动态码有效时间(单位:秒 30s~60s之间):刷新请提前5s", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    @Min(30)
    @Max(60)
    private Integer validSeconds;
    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_DYNAMICCODE_GET;
    }
}
