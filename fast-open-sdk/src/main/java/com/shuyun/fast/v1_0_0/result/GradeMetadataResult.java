package com.shuyun.fast.v1_0_0.result;

import com.shuyun.fast.v1_0_0.domain.GradeRuleGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class GradeMetadataResult implements Serializable {
    @Schema(title = "等级/勋章id")
    private String gradeId;
    @Schema(title = "等级/勋章名称")
    private String gradeName;
    @Schema(title = "等级/勋章序号")
    private Integer gradeSort;
    @Schema(title = "升级规则")
    private GradeRuleGroup upgradeRule;
    @Schema(title = "保级规则")
    private GradeRuleGroup stayGradeRule;
}
