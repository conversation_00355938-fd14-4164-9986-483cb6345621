package com.shuyun.fast.v1_0_0.param.tag;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class TagUserTagsParam extends ApiBaseParam {
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "标签id集合(请提前去重)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private List<String> tagIds;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_TAG_USER_TAGS;
    }
}
