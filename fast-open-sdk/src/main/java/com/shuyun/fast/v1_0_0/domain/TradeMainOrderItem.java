package com.shuyun.fast.v1_0_0.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class TradeMainOrderItem extends MemberId {
    @Schema(hidden = true, title = "对象id")
    private String id;
    @Schema(title = "消费门店类型code", type = "String", example = "TAOBAO")
    private String shopTypeCode;
    @NotEmpty
    @Schema(title = "渠道", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 20, example = "TAOBAO")
    private String channelType;
    @NotNull
    @Schema(title = "应付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "85")
    private Double totalFee;
    @NotNull
    @Schema(title = "正常单时，代表实付金额；退单时，代表退款金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "85")
    private Double payment;
    @Schema(title = "折扣率 <=1", type = "Double", example = "0.85")
    private Double discountRate;
    @Schema(title = "折扣金额", type = "Double", example = "15")
    private Double discountFee;
    @NotEmpty
    @Schema(title = "子订单ID", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 128, example = "bf3a654f1afa4859bb5c0e8846bxxxxx")
    private String orderItemId;
    @Schema(title = "原始子订单ID, 退单时传", example = "bf3a654f1afa4859bb5c0e8846bxxxxx")
    private String originOrderItemId;
    @NotEmpty
    @Schema(title = "商品Code", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 64, example = "P001")
    private String productCode;
    @NotEmpty
    @Schema(title = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 255, example = "P001")
    private String productName;
    @NotNull
    @Schema(title = "商品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer quantity;
    @NotEmpty
    @Schema(title = "子订单状态", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 32, example = "FINISHED")
    private String status;
    @Schema(title = "商品批次编码", example = "S001")
    private String skuId;
    @Schema(title = "吊牌价", example = "100")
    private Double tagPrice;
    @Schema(title = "零售价", example = "100")
    private Double retailPrice;
    @Schema(title = "子订单类型:正常单 - NORMAL,  退单 - REFUND", example = "NORMAL")
    private String orderType;
    @Schema(title = "商品图片url", example = "[\"https://allpro.saas.top/FsB-uzzX4WtCzpDu-Vuwqp2OdNbQ?e=1547742055&token=yNjhOp7gnH4St9yP72OlwuQ6JUNQf49pxAuGWYFt:1eZIUjRKIekGuMGG-c-s7UqNF1Y=;\"]")
    private List<String> picture;

    @Schema(title = "8时区下单时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;
    @Schema(title = "8时区订单完成时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;
    @Schema(title = "8时区更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    @Schema(hidden = true, title = "8时区数据同步时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSync;
    @NotEmpty
    @Schema(title = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 128, example = "087989aaf6af4e58897794d18axxxxx")
    private String orderId;
    @Schema(title = "消费门店名称", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 255, example = "SHOP001")
    private String shopName;
    @Schema(title = "消费门店code", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 64, example = "SHOP001")
    private String shopCode;
    @Schema(title = "订单扩展字段", example = "{\"string\":\"update\"}")
    private Map<String, Object> extension;
}
