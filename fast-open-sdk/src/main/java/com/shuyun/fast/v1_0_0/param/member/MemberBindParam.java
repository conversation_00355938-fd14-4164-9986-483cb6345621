package com.shuyun.fast.v1_0_0.param.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class MemberBindParam extends ApiBaseParam {

    @Schema(title = "注册场景", example = "直播")
    private String scene;
//    private String brand;
//    private String customerNo;
    @Schema(title = "会员识别对象(memberId与userId二者均必输)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "8时区绑定时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime bindTime;
    @Schema(title = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 32, example = "18998780000")
    @NotBlank
    private String mobile;
    @Schema(title = "渠道联合id", example = "xxssxxx")
    private String unionId;
    @Schema(title = "微信appId", example = "appId28154751841")
    private String appId;
    @Schema(title = "微信openId", example = "openId28154751841")
    private String openId;
    @Schema(hidden = true, title = "入会渠道类型", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 32, example = "TAOBAO")
//    @NotBlank
    private String registerChannel;
    @Schema(title = "入会店铺code", example = "taobaoshop1")
    private String registerShopCode;
    @Schema(title = "入会店铺名称", example = "淘宝旗舰店")
    private String registerShopName;
    @Schema(title = "归属渠道", example = "ELme")
    private String ascriptionChannel;
    @Schema(title = "归属渠道名称", example = "饿了么")
    private String ascriptionChannelName;
    @Schema(title = "归属门店code", example = "TA1002")
    private String ascriptionShopCode;
    @Schema(title = "归属门店名称", example = "淘宝旗舰店")
    private String ascriptionShopName;
    @Schema(title = "会员姓名", example = "张小图")
    private String fullName;
    @Schema(title = "名字", example = "小图")
    private String firstName;
    @Schema(title = "姓", example = "张")
    private String lastName;
    @Schema(title = "头像", example = "http://www.baidu.com/xudwdwdw")
    private String headImgUrl;
    @Schema(title = "昵称", example = "小叶")
    private String nick;
    @Schema(title = " 性别： F:女, M:男, O:其他", example = "F")
    private String gender;
    @Schema(title = "证件类型", description = "identityCard:身份证,passport：护照,drivingLicence：驾驶证", example = "identityCard")
    private String certificateType;
    @Schema(title = "证件号码", example = "******************")
    private String certificateNo;
    @Schema(title = "邮箱", example = "<EMAIL>")
    private String email;
    @Schema(title = "生日", example = "2001-09-09")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String dateOfBirth;
    @Schema(title = "国籍", example = "中国")
    private String nationality;
    @Schema(title = "省", example = "上海")
    private String province;
    @Schema(title = "城市", example = "上海")
    private String city;
    @Schema(title = "地区", example = "长宁区")
    private String district;
    @Schema(title = "邮编", example = "657654")
    private String postalCode;
    @Schema(title = "联系地址（中文）", example = "凯旋路1009号")
    private String address;
    @Schema(title = "工作", example = "软件工程师")
    private String job;
    @Schema(title = "学历", example = "大学本科")
    private String education;
    @Schema(title = "行业", example = "互联网/软件")
    private String industry;
    @Schema(title = "爱好", example = "运动")
    private String hobby;
    @Schema(title = "收入", example ="10086" )
    private String income;
    @Schema(title = "婚姻状态", description = "M:已婚 S:未婚 D:离异 O:其他", example = "S")
    private String marriage;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "结婚纪念日", description = "格式: yyyy-MM-dd", example = "2018-09-10")
    private String anniversary;
    @Schema(title = "是否有孩子", description = "Y:是 N:否 默认:N", example = "N")
    private String hasChildren;
    @Schema(title = "推荐人", example = "小檀")
    private String recommender;
    @Schema(title = "注册导购工号", example = "1092")
    private String registerGuide;
    @Schema(title = "服务导购工号", example = "JU1001")
    private String serviceGuide;
    @Schema(title = "应用类型编码", example = "EBRAND")
    private String appType;
    @Schema(title = "扩展属性")
    private Map<String, Object> extras;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_BIND;
    }
}
