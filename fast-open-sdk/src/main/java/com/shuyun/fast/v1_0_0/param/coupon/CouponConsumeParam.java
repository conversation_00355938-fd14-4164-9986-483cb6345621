package com.shuyun.fast.v1_0_0.param.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.domain.Coupon;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.ticket.benefit.vo.Order;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class CouponConsumeParam extends CouponBaseParam {
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)")
    private MemberIdentifyParam identify;
//    @Schema(hidden = true, title = "校验规则或保存实例扩展字段", description = "对应卡券方案-规则-数据配置中的校验数据字段名。依规则配置确定是否对传入字段进行校验，数据会被保存到实例模型")
//    private Map<String, Object> extension;
    @Schema(title = "劵码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private List<Coupon> coupons;
    @Schema(title = "店铺code")
    private String shopCode;
    @Schema(title = "平台Id")
    private String platformId;
    @Schema(title = "订单信息")
    private Order order;
    @Schema(title = "锁定事务ID,锁定时使用的事务ID,解锁时必须传送")
    private String lockTransactionId;
    @Schema(title = "是否是校验场景", hidden = true)
    private Boolean isCheck = false;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_CONSUME;
    }
}
