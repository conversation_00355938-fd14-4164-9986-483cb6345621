package com.shuyun.fast.v1_0_0.param.checklist;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RiskControlChecklistGetResponse {
    @Schema(title = "id")
    private String id;
    @Schema(title = "黑名单类型 BLACK:客户 MOBILE:手机 EMAIL:邮箱")
    private String type;
    @Schema(title = "主题id")
    private String subjectId;
    @Schema(title = "客户编号")
    private String customerno;
    @Schema(title = "添加方式")
    private String entryMode;
    @Schema(title = "更新时间")
    private String lastUpdate;
    @Schema(title = "最后一次操作人")
    private String lastOperator;
    @Schema(title = "分组id")
    private String groupId;
    @Schema(title = "备注")
    private String remark;
}
