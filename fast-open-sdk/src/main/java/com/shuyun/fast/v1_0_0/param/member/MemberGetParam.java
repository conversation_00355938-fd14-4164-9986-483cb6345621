package com.shuyun.fast.v1_0_0.param.member;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.ticket.util.JsonUtil;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.lang.reflect.Method;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class MemberGetParam extends ApiBaseParam {
    @Schema(title = "手机号:与unionId、identify对象三选一必输")
    private String mobile;
    @Schema(title = "unionId:与mobile、identify对象三选一必输")
    private String unionId;
    @Schema(title = "会员识别对象,与mobile、unionId字段三选一必输(内部memberId与userId二者仅可选其一)")
    private MemberIdentifyParam identify;
    @Schema(title = "自定义查询字段")
    private List<String> optionalFields;
    @Schema(title = "是否需要渠道数据:不传默认false", defaultValue = "false")
    private Boolean channelDataRequired = false;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_GET;
    }

    public static void main(String[] args) throws Exception{
        MemberGetParam param = new MemberGetParam();
        System.out.println(JsonUtil.serialize(param));
        Method m = param.getClass().getDeclaredMethod("setMobile", String.class);
        m.invoke(param, "666666");
        System.out.println(JsonUtil.serialize(param));
    }

}
