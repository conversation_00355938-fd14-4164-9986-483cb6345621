package com.shuyun.fast.v1_0_0.param.point;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class PointRecordsGetParam extends PageParam {
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "积分业务类型,不传,默认为: POINT", defaultValue = "POINT")
    private String pointBizType = "POINT";
    @Schema(title = "积分变更类型 SEND: 立即发放, DELAY_SEND: 延迟发放, EXPIRE: 过期, FREEZE: 冻结, UNFREEZE: 取消冻结, DEDUCT: 扣减, ABOLISH: 作废, TIMER: 定时, RECALCULATE: 废弃重算, SPECIAL_DEDUCT: 特殊扣除, SPECIAL_FREEZE: 特殊冻结, SPECIAL_UNFREEZE: 特殊解冻, SPECIAL_ABOLISH: 特殊废弃, MANUAL_ABOLISH: 手动废弃, OPEN_FREEZE: 接口冻结, OPEN_UNFREEZE: 接口解冻", example = "SEND")
    private List<String> recordTypes;
    @Schema(title = "状态 DELAY:待生效, DELAY_FROZEN:待生效已冻结, DELAY_ABOLISH:待生效已作废, EXPIRE:已生效已过期, VALID:已生效, FROZEN:已生效已冻结, USED:已生效已使用, ABOLISH:已生效已作废, FROZEN_ABOLISH:已生效已冻结已作废", example = "VALID")
    private List<String> status;
    @Schema(title = "店铺code")
    private String shopCode;
    @Schema(title = "原单ID")
    private String traceId;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_POINT_RECORDS_GET;
    }
}
