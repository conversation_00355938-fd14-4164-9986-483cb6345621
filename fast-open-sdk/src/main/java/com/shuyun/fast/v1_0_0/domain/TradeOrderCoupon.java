package com.shuyun.fast.v1_0_0.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.base.ModelTag;
import com.shuyun.fast.base.ModelTags;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
public class TradeOrderCoupon implements Serializable, ModelTag {
    @Schema(hidden = true, title="会员类型")
    private String memberType;
    @Schema(hidden = true, title = "对象id")
    private String id;
    @Schema(hidden = true, title = "渠道", example = "TAOBAO")
    private String channelType;
    @Schema(title = "子订单号", example = "XJLO00001")
    private String orderItemId;
    @Schema(title = "优惠券项目id", example = "")
    private String projectId;
    @Schema(title = "优惠券项目名称", example = "全品类8折券")
    private String projectName;
    @Schema(title = "优惠券实例id", example = "")
    private String instanceId;
    @Schema(title = "优惠券码", example = "O1JH200D")
    private String couponNo;
    @Schema(title = "优惠金额", example = "9.99")
    private Double couponFee;
    @Schema(hidden = true, title = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "087989aaf6af4e58897794d18axxxxx")
    private String orderId;
    @Schema(hidden = true, title = "8时区最后更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSync = LocalDateTime.now();
    @Override
    public String fqn() {
        return ModelTags.DATA_FQN_TRADE_MEMBER_ORDER_COUPON;
    }
}
