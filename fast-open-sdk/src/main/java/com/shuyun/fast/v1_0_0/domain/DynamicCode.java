package com.shuyun.fast.v1_0_0.domain;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@Introspected
@AllArgsConstructor
public class DynamicCode {
    @Schema(title = "动态码", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 32)
    @NotBlank
    private String dynamicCode;
}
