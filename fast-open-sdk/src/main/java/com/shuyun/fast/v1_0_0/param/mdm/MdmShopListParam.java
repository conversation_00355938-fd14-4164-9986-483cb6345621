package com.shuyun.fast.v1_0_0.param.mdm;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuyun.fast.base.*;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class MdmShopListParam extends PageParam implements ModelTag {
    @Schema(title = "营业状态", example = "OPENING")
    private String status;
//    @Schema(title = "门店定位经度", example = "121.346301")
//    private String longitude;
//    @Schema(title = "门店定位纬度", example = "31.319295")
//    private String latitude;
    @Override
    public String apiName() {
        return ApiTags.API_NAME_MDM_SHOP_LIST;
    }

    @Override
    public String fqn() {
        return ModelTags.DATA_FQN_MDM_SHOP;
    }
}
