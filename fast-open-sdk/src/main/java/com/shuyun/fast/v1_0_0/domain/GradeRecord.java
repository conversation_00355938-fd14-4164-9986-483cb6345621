package com.shuyun.fast.v1_0_0.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class GradeRecord extends MemberId {

    @Schema(title = "变更前等级ID")
    private Long originalGradeId;
    @Schema(title = "变更前等级名称")
    private String originalGradeName;
    @Schema(title = "变更后等级ID")
    private Long currentGradeId;
    @Schema(title = "变更后等级名称")
    private String currentGradeName;
    @Schema(title = "等级变更类型. UPGRADE:升级,DEGRADE:降级,HOLD_BACK_GRADE:保级", example = "UPGRADE")
    private String recordType;
    @Schema(title = "描述", type = "String")
    private String description;
    @Schema(title = "8时区变更前等级生效时间  ,格式为:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime originalEffectTime;
    @Schema(title = "8时区变更前等级过期时间  ,格式为:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime originalExpireTime;
    @Schema(title = "8时区变更后等级过期时间  ,格式为:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime currentEffectTime;
    @Schema(title = "8时区变更后等级过期时间  ,格式为:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime currentExpireTime;
    @Schema(title = "8时区等级变更时间  ,格式为:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime changeTime;
}
