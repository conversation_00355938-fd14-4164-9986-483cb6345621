package com.shuyun.fast.v1_0_0.param.member;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class MemberDynamicCodeIdentifyParam extends ApiBaseParam {
    @Schema(title = "动态码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String dynamicCode;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_DYNAMICCODE_IDENTIFY;
    }
}
