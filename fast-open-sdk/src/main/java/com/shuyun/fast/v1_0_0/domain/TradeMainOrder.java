package com.shuyun.fast.v1_0_0.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class TradeMainOrder extends MemberId {
    @Schema(title = "消费门店类型code", example = "TAOBAO")
    private String shopTypeCode;
    @NotEmpty
    @Schema(title = "渠道", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 20, example = "TAOBAO")
    private String channelType;
    @NotNull
    @Schema(title = "应付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "85")
    private Double totalFee;
    @NotNull
    @Schema(title = "正常单时，代表实付金额；退单时，代表退款金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "85")
    private Double payment;
    @Schema(title = "折扣率 <=1", example = "0.85")
    private Double discountRate;
    @Schema(title = "折扣金额", example = "15")
    private Double discountFee;
    @Schema(title = "订单扩展字段", example = "{\"string\":\"update\"}")
    private Map<String, Object> extension;

    @Schema(title = "客户编号（单渠道唯一标识）", example = "T000001")
    private String customerNo;
    @NotEmpty
    @Schema(title = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "087989aaf6af4e58897794d18axxxxx")
    private String orderId;
    @Schema(title = "原始订单ID, 退单时传", example = "087989aaf6af4e58897794d18axxxxx")
    private String originOrderId;
    @Schema(title = "消费门店code", requiredMode = Schema.RequiredMode.REQUIRED, example = "SHOP001")
    private String shopCode;
    @Schema(title = "消费门店名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "SHOP001")
    private String shopName;
    @NotNull
    @Schema(title = "商品总数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer totalQuantity;
    @NotEmpty
    @Schema(title = "订单类型:正常单-NORMAL,退单-REFUND,赠品订单-GIFT。更多可以去字典表配置", requiredMode = Schema.RequiredMode.REQUIRED, example = "NORMAL")
    private String orderType;
    @NotEmpty
    @Schema(title = "订单状态:CREATED-下单,CANCELLED-订单取消,DELIVERED-已发货,CONFIRMED-卖家已确认,FINISHED-完成,REFUND_START-退款开始,REFUND_FINISHED-退款完成.更多请去字典表配置", requiredMode = Schema.RequiredMode.REQUIRED, example = "FINISHED")
    private String orderStatus;
    @Schema(title = "运费", example = "10")
    private Double freight;
    @Schema(title = "收货人姓名", example = "abc")
    private String receiverName;
    @Schema(title = "收货人手机号", example = "18900000000")
    private String receiverMobile;
    @Schema(title = "收货人固定电话", example = "18900000000")
    private String receiverTelephone;
    @Schema(title = "省", example = "上海")
    private String receiverProvince;
    @Schema(title = "市", example = "上海")
    private String receiverCity;
    @Schema(title = "区/县", example = "浦东新区")
    private String receiverDistrict;
    @Schema(title = "收货人地址", example = "南京东路100号")
    private String receiverAddress;
    @Schema(title = "邮编", example = "200000")
    private String receiverZipCode;
    @Schema(title = "优惠劵金额", example = "15")
    private Double couponFee;
    @Schema(title = "订单对应导购", example = "001")
    private String guideCode;
    @Schema(title = "订单备注", example = "无")
    private String description;
    @Schema(title = "是否计算积分. 0-不计算积分,  1- 计算积分", example = "1")
    private int pointFlag = 1;
    @Schema(title = "是否是内部订单. Y:否  N:是 . 默认为N", example = "N")
    private String isInternal;
    @Schema(hidden = true, title = "isSend, 默认为N", example = "N")
    private String isSend;

    @Schema(hidden = true, title = "订单主键ID")
    private String id;
    @Schema(title = "8时区订单付款时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;
    @Schema(title = "8时区订单发货时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shippingTime;
    @Schema(title = "8时区订单收货时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiveTime;
    @Schema(title = "8时区订单换货时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exchangeTime;
    @Schema(title = "8时区订单退货时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundTime;
    @Schema(title = "8时区订单完成时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;
    @Schema(title = "8时区下单时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;
    @Schema(title = "8时区更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    @Schema(hidden = true, title = "8时区数据同步时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSync;
    private List<TradeMainOrderItem> orderItems;
}
