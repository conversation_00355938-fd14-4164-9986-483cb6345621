package com.shuyun.fast.v1_0_0.param.member;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "会员识别参数")
@Introspected
public class MemberIdentifyParam {
    @Schema(title = "麒麟会员ID")
    private String memberId;
    @Schema(title = "识别渠道 POS;WECHAT:微信 非必输(如果不传默认取requestChannel)")
    private String channel;
    @Schema(title = "渠道用户id。POS渠道:pos卡号(不能为手机号);微信:appId_openId;淘宝渠道:ouid;京东:xid;抖店:openid.其他渠道唯一id等等", example = "wxd678efh567hg6787_oGPAG5rdBP78jh_3cmJ1en8FvZlo")
    private String userId;
}
