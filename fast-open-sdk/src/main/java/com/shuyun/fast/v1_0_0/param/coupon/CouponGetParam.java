package com.shuyun.fast.v1_0_0.param.coupon;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class CouponGetParam extends ApiBaseParam {

    @Schema(title = "项目id",maxLength = 32)
    private String projectId;
    @Schema(title = "券码", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 128)
    @NotEmpty
    private String code;
    @Schema(title = "是否展示项目信息", description = "为false的话响应参数中没有project")
    private Boolean showProject = false;
    @Schema(title = "是否展示选择器数据:默认false", description = "为false的话响应参数不返回任何选择器数据")
    private Boolean showSelectorData = false;
    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_GET;
    }
}
