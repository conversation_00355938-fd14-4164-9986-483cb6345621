package com.shuyun.fast.v1_0_0.param.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class CouponGrantParam extends CouponBaseParam {
    @Schema(title = "会员识别对象(具名券发放时memberId与userId二者仅可选其一)")
    private MemberIdentifyParam identify;
    @Schema(title = "项目Id", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 32)
    @NotBlank
    private String projectId;
    @NotNull
    @Min(value = 1)
    @Max(value = 20)
    @Schema(title = "发放数量:最小1最大限制默认是20", requiredMode = Schema.RequiredMode.REQUIRED, defaultValue = "1")
    private Integer grantNum = 1;
    @Schema(hidden = true, title = "发放渠道")
    private String grantPlatform;
    @Schema(title = "发放店铺")
    private String grantShop;
    @Schema(title = "发放原因")
    private String grantReason;
    @Schema(hidden = true, title = "来源模块")
    private String sourceModule;//暂不对外
    @Schema(hidden = true, title = "营销节点")
    private String marketingNodeId;//暂不对外
    @Schema(hidden = true, title = "营销活动Id")
    private String marketingActivityId;//暂不对外
//    @Schema(hidden = true, title = "校验规则或保存实例扩展字段", description = "对应卡券方案-规则-数据配置中的校验数据字段名。依规则配置确定是否对传入字段进行校验，数据会被保存到实例模型")
//    private Map<String, Object> extension;//暂不对外

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_GRANT;
    }
}
