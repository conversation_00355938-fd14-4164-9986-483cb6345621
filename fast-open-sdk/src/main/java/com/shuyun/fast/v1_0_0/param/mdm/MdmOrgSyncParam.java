package com.shuyun.fast.v1_0_0.param.mdm;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.ModelTag;
import com.shuyun.fast.base.ModelTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
@JsonFilter("mdmOrgSyncParamFilter")
public class MdmOrgSyncParam extends ApiBaseParam implements ModelTag{
    @Schema(hidden = true, title="会员类型")
    private String memberType;
    @Schema(hidden = true, title = "对象id")
    private String id;
    @Schema(hidden = true, title = "组织id")
    private String orgId;
    @Schema(title = "组织编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orgCode;
    @Schema(title = "组织名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orgName;
    @Schema(title = "级别", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer tier;//级别
    @Schema(hidden = true, title = "父组织id")
    private String parentId;
    @Schema(title = "父组织编码")
    private String orgParentCode;
    @Schema(title = "父组织名称")
    private String parentName;
    @Schema(title = "排序")
    private Integer orderNum;//排序
    @Schema(title = "")
    private Integer leader;
    @Schema(title = "状态")
    private Integer status;//状态
    @Schema(title = "8时区创建时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @Schema(title = "8时区更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    @Schema(hidden = true, title = "8时区最后更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSync = LocalDateTime.now();
    @Schema(title = "扩展字段")
    private Map<String, Object> extension = new HashMap<String, Object>();
    @JsonAnySetter
    public void setExtension(String key, Object value) {
        this.extension.put(key, value);
    }
    //enabled = true开启后本方法将卡券自定义属性动态扩展到本对象最外层,如果想统一放到extData对象则请设置为false
    @JsonAnyGetter(enabled = true)
    public Map<String, Object> getExtension() {
        return this.extension;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MDM_ORG_SYNC;
    }

    @Override
    public String fqn() {
        return ModelTags.DATA_FQN_MDM_ORG;
    }
}
