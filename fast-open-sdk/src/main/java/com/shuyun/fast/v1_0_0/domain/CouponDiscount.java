package com.shuyun.fast.v1_0_0.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CouponDiscount implements Serializable {
    @Schema(title = "实例标识")
    private Coupon identity;
    @Schema(title = "是否在订单上使用",
            description = "如果权益实例既没有在订单上使用，也没有在子订单上使用，则表示该权益实例没用上，不需要核销")
    private Boolean orderUsed;
    @Schema(title = "是否在子订单上使用",
            description = "如果权益实例既没有在订单上使用，也没有在子订单上使用，则表示该权益实例没用上，不需要核销")
    private Boolean orderItemUsed;
    @Schema(title = "使用的子订单ID列表")
    private List<String> orderItemIds;
}
