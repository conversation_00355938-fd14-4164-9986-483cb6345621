package com.shuyun.fast.v1_0_0.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class Coupon extends MemberId{
    @Schema(title = "卡券实例ID")
    private String id;
    @Schema(title = "项目ID")
    private String projectId;
    @Schema(title = "卡券券码", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 128)
    @NotBlank
    private String code;
    @Schema(title = "发放时间:8时区格式")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime grantAt;
    @Schema(title = "实例状态")
    private String state;
}
