package com.shuyun.fast.v1_0_0.param.checklist;

import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class ChecklistGetParam extends ApiBaseParam {
    @Schema(title = "会员识别对象", requiredMode = Schema.RequiredMode.REQUIRED)
    private MemberIdentifyParam identify;
    @Schema(title = "黑名单类型 BLACK:客户 MOBILE:手机 EMAIL:邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checklistType;
    @Schema(title = "黑名单分组id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String groupId;
    @Override
    public String apiName() {
        return ApiTags.API_NAME_CHECKLIST_GET;
    }
}
