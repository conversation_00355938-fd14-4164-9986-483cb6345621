package com.shuyun.fast.v1_0_0.domain;
import com.fasterxml.jackson.annotation.*;
import com.shuyun.fast.base.ModelTag;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.v1_0_0.constant.OrderOwnerType;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@JsonFilter("tradeRefundOrderItemFilter")
public class TradeRefundOrderItem implements Serializable, ModelTag {
    @Schema(hidden = true, title="会员类型")
    private String memberType;
    @Schema(hidden = true, title = "订单归属类型 consumer-消费者  member-会员")
    @NotNull
    @JsonIgnore
    private String orderOwnerType;
    @Schema(hidden = true, title = "对象id")
    private String id;
    @Schema(title = "消费门店类型code", example = "TAOBAO")
    private String shopTypeCode;
    @Schema(hidden = true, title = "渠道", example = "TAOBAO")
    private String channelType;
    @Schema(title = "订单扩展字段", example = "{\"string\":\"update\"}")
    private Map<String, Object> extension = new HashMap<String, Object>();
    @JsonAnySetter
    public void setExtension(String key, Object value) {
        this.extension.put(key, value);
    }
    //enabled = true开启后本方法将卡券自定义属性动态扩展到本对象最外层,如果想统一放到extData对象则请设置为false
    @JsonAnyGetter(enabled = true)
    public Map<String, Object> getExtension() {
        return this.extension;
    }
    @Schema(title = "8时区更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    @Schema(hidden = true, title = "8时区下单时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;
    @Schema(title = "8时区退货完成时间", description = "格式: yyyy-MM-dd HH:mm:ss 退货完成场景必填", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;
    @NotNull
    @Schema(title = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "85")
    @JsonProperty("payment")
    private Double payment;
    @Schema(hidden = true, title = "订单应付总金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "85")
    private Double totalFee;
    @Schema(hidden = true, title = "原始主订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "087989aaf6af4e58897794d18axxxxx")
    private String originOrderId;
//    @NotEmpty
    @Schema(title = "原始子订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "bf3a654f1afa4859bb5c0e8846bxxxxx")
    private String originOrderItemId;
    @NotEmpty
    @Schema(title = "子订单状态:REFUND_START-退款开始,REFUNDING-退款中,REFUND_FINISHED-退款完成,REFUND_CANCEL-退款取消,SELLING_REFUND_FINISHED-售中退单完成.更多请去字典表配置", requiredMode = Schema.RequiredMode.REQUIRED, example = "FINISHED")
    private String status;
    @Schema(title = "退单主订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "087989aaf6af4e58897794d18axxxxx")
    @JsonProperty("orderId")
    private String orderId;
    @Schema(title = "子订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "bf3a654f1afa4859bb5c0e8846bxxxxx")
    @JsonProperty("orderItemId")
    private String orderItemId;
    @NotEmpty
    @Schema(title = "商品Code", requiredMode = Schema.RequiredMode.REQUIRED, example = "P001")
    private String productCode;
    @NotEmpty
    @Schema(title = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "P001")
    private String productName;
    @NotNull
    @Schema(title = "商品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer quantity;
    @Schema(title = "商品批次编码", example = "S001")
    private String skuId;
    @Schema(title = "商品图片url", example = "[\"https://allpro.saas.top/FsB-uzzX4WtCzpDu-Vuwqp2OdNbQ?e=1547742055&token=yNjhOp7gnH4St9yP72OlwuQ6JUNQf49pxAuGWYFt:1eZIUjRKIekGuMGG-c-s7UqNF1Y=;\"]")
    private List<String> picture;
    @Schema(hidden = true, title = "8时区最后更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSync = LocalDateTime.now();
    @Schema(title = "消费门店code", requiredMode = Schema.RequiredMode.REQUIRED, example = "SHOP001")
    private String shopCode;
    @Schema(title = "消费门店名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "SHOP001")
    private String shopName;
    @Schema(hidden = true, title = "订单类型:退单-REFUND", example = "REFUND")
    private String orderType = "REFUND";
    @Override
    public String fqn() {
        if(orderOwnerType.equals(OrderOwnerType.MEMBER)){
            return ModelTags.DATA_FQN_TRADE_MEMBER_REFUND_ITEM;
        }
        return ModelTags.DATA_FQN_TRADE_CONSUMER_REFUND_ITEM;
    }
}
