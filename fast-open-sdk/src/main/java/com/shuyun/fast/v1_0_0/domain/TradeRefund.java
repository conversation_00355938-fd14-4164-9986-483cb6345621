package com.shuyun.fast.v1_0_0.domain;

import com.fasterxml.jackson.annotation.*;
import com.shuyun.fast.base.ModelTag;
import com.shuyun.fast.base.ModelTags;
import com.shuyun.fast.v1_0_0.constant.OrderOwnerType;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonFilter("tradeRefundFilter")
public class TradeRefund extends MemberId implements ModelTag {
    @Schema(hidden = true, title="会员类型")
    private String memberType;
    @Schema(hidden = true, title = "订单归属类型 consumer-消费者  member-会员")
    @NotNull
    @JsonIgnore
    private String orderOwnerType;
    @Schema(hidden = true, title = "麒麟会员对象")
    private Map<String, String> member;
    @Schema(hidden = true, title = "对象id")
    private String id;
    @Schema(title = "消费门店类型code", type = "String", example = "TAOBAO")
    private String shopTypeCode;
    @Schema(hidden = true, title = "渠道", example = "TAOBAO")
    private String channelType;
    @Schema(title = "8时区退货时间", description = "格式: yyyy-MM-dd HH:mm:ss",  requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundTime;
    @Schema(hidden = true, title = "8时区下单时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;
    @Schema(title = "8时区退货完成时间", description = "格式: yyyy-MM-dd HH:mm:ss 退货完成场景必填", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;
    @NotNull
    @Schema(title = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "1.00")
    @JsonProperty("payment")
    private Double payment;
    @Schema(hidden = true, title = "订单应付总金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "85")
    private Double totalFee;
    @Schema(title = "原始主订单ID", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 128, example = "087989aaf6af4e58897794d18axxxxx")
    private String originOrderId;
    @NotEmpty
    @Schema(title = "订单状态:REFUND_START-退款开始,REFUNDING-退款中,REFUND_FINISHED-退款完成,REFUND_CANCEL-退款取消,SELLING_REFUND_FINISHED-售中退单完成.更多请去字典表配置", requiredMode = Schema.RequiredMode.REQUIRED, example = "FINISHED")
    private String orderStatus;
    @Schema(title = "子退单列表")
    private List<TradeRefundOrderItem> refundOrderItems = new ArrayList();
    @Schema(hidden = true, title = "客户编号（单渠道唯一标识）", example = "T000001")
    private String customerNo;
    @NotEmpty
    @Schema(title = "退单主订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "087989aaf6af4e58897794d18axxxxx")
    @JsonProperty("orderId")
    private String orderId;
    @Schema(title = "消费门店code", requiredMode = Schema.RequiredMode.REQUIRED, example = "SHOP001")
    @NotEmpty
    private String shopCode;
    @Schema(title = "消费门店名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "SHOP001")
    @NotEmpty
    private String shopName;
    @NotNull
    @Schema(title = "商品总数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer totalQuantity;
    @Schema(title = "运费", example = "10")
    private Double freight;
    @Schema(title = "收货人姓名", example = "abc")
    private String receiverName;
    @Schema(title = "收货人手机号", example = "18900000000")
    private String receiverMobile;
    @Schema(title = "收货人固定电话", example = "18900000000")
    private String receiverTelephone;
    @Schema(title = "省", example = "上海")
    private String receiverProvince;
    @Schema(title = "市", example = "上海")
    private String receiverCity;
    @Schema(title = "区/县", example = "浦东新区")
    private String receiverDistrict;
    @Schema(title = "收货人地址", example = "南京东路100号")
    private String receiverAddress;
    @Schema(title = "邮编", example = "200000")
    private String receiverZipCode;
    @Schema(title = "退单备注", example = "无")
    private String description;
    @Schema(title = "是否计算积分. 0-不计算积分,  1- 计算积分", example = "1")
    private int pointFlag = 1;
    @Schema(hidden = true, title = "8时区最后更新时间", description = "格式: yyyy-MM-dd HH:mm:ss", example = "2023-03-09 10:28:57")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSync = LocalDateTime.now();
    @Schema(title = "退单扩展字段", example = "{\"string\":\"update\"}")
    private Map<String, Object> extension = new HashMap<String, Object>();
    @JsonAnySetter
    public void setExtension(String key, Object value) {
        this.extension.put(key, value);
    }
    //enabled = true开启后本方法将卡券自定义属性动态扩展到本对象最外层,如果想统一放到extData对象则请设置为false
    @JsonAnyGetter(enabled = true)
    public Map<String, Object> getExtension() {
        return this.extension;
    }
    @Schema(hidden = true, title = "是否推忠诚度. Y:否  N:是 . 默认为N", example = "N")
    private String isSend = "N";
    @Schema(title = "是否是内部订单. Y:否  N:是 . 默认为N", example = "N")
    private String isInternal = "N";
    @Schema(hidden = true, title = "订单类型:退单-REFUND", example = "REFUND")
    private String orderType = "REFUND";

    @Override
    public String fqn() {
        if(orderOwnerType.equals(OrderOwnerType.MEMBER)){
            return ModelTags.DATA_FQN_TRADE_MEMBER_REFUND;
        }
        return ModelTags.DATA_FQN_TRADE_CONSUMER_REFUND;
    }
}
