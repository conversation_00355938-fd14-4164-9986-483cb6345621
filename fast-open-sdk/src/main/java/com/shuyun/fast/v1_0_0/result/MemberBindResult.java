package com.shuyun.fast.v1_0_0.result;

import com.shuyun.fast.v1_0_0.domain.MemberId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MemberBindResult extends MemberId {
    @Schema(title = "注册状态", example = "NEW", requiredMode = Schema.RequiredMode.REQUIRED, description = "NEW(新会员),BINDING(绑定),REGISTERED(重复绑定)")
    private String status;
}
