package com.shuyun.fast.v1_0_0.result;

import com.shuyun.fast.v1_0_0.domain.GradeBudgetDifferenceGroup;
import com.shuyun.fast.v1_0_0.domain.GradeRuleGroup;
import com.shuyun.fast.v1_0_0.domain.MemberId;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class GradeBudgetResult extends MemberId {
    @Schema(title = "等级id", example = "600006")
    private String gradeId;
    @Schema(title = "等级名称", example = "VIP1")
    private String gradeName;
    @Schema(title = "升级到下一等级还需达成条件")
    private GradeBudgetDifferenceGroup upgradeDifference;
}
