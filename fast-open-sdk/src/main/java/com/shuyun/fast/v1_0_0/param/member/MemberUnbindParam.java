package com.shuyun.fast.v1_0_0.param.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class MemberUnbindParam extends ApiBaseParam {
    @Schema(title = "会员识别对象(memberId与userId二者均必输)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(hidden = true, title = "注册渠道code,支持 TAOBAO:淘宝,JD:京东,YOUZAN:有赞,DOUYIN:抖音,VIP:唯品会,WEIMOB:微盟,WECHAT:微信,POS:线下,OTHER:其他", description = "渠道id对应的入会渠道", example = "TAOBAO")
    private String registerChannel;
    @Schema(hidden = true, title = "注册店铺code", example = "shop001")
    private String registerShopCode;
    @Schema(title = "8时区解绑时间,格式:yyyy-MM-dd HH:mm:ss", example = "2021-12-30 02:24:02")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime unbindTime;

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_UNBIND;
    }
}
