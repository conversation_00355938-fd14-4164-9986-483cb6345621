package com.shuyun.fast.v1_0_0.param.grade;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class GradeRecordsGetParam extends PageParam {
    @Schema(description = "会员识别对象(memberId与userId二者仅可选其一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private MemberIdentifyParam identify;
    @Schema(title = "等级业务类型,不传,默认为: GRADE", defaultValue = "GRADE")
    private String gradeBizType = "GRADE";

    @Override
    public String apiName() {
        return ApiTags.API_NAME_GRADE_RECORDS_GET;
    }
}
