package com.shuyun.fast.douyin.param;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/7/1
 */
@Schema(title = "抖店会员上行请求")
@Introspected
public class DoudianSyncRequest {
    @Schema(title = "抖店应用ID",description = "固定为1",defaultValue = "1")
    private Integer app_id =1;
    @Schema(title = "抖店会员信息列表")
    private List<DoudianMemberInfo> member_info_list = new ArrayList();

    public Integer getApp_id() {
        return app_id;
    }

    public void setApp_id(Integer app_id) {
        this.app_id = app_id;
    }

    public List<DoudianMemberInfo> getMember_info_list() {
        return member_info_list;
    }

    public void setMember_info_list(List<DoudianMemberInfo> member_info_list) {
        this.member_info_list = member_info_list;
    }
}
