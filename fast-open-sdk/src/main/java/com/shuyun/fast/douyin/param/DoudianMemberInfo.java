package com.shuyun.fast.douyin.param;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/7/1
 */
@ToString
@Introspected
public class DoudianMemberInfo {
    @Schema(title =  "会员手机号")
    private String mobile;
    // openid----memberbinding里面是nick；会籍是openid
    @Schema(title =  "抖店open_id")
    private String open_id;

    // 老的积分值，待废弃   抖音平台后续用pointCent代替integral
    @Deprecated
    @Hidden
    private Integer integral;

    // 忠诚度等级id
    @Schema(title =  "会员等级ID",description = "此等级为会员的麒麟忠诚度等级ID")
    private Integer level;

    @Schema(title =  "是否注销",description = "默认为false, true:表示注销抖店平台会员;",defaultValue = "false")
    //  是否注销
    private Boolean unbind = false;
    // 店铺code
    @Schema(title =  "抖店店铺code")
    private String shopCode;
    // 最新积分，单位到分。 需要 放大100倍再同步
    @Schema(title =  "会员当前积分(单位到分)",description = "需要将麒麟会员忠诚度积分* 100")
    private Long pointCent;

    //unionId，品牌会员必传
    @Schema(title =  "抖店union_id", description = "若此店铺是品牌会员通，则union_id必传;若此店铺为店铺会员通,则union_id不用传")
    private String union_id;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOpen_id() {
        return open_id;
    }

    public void setOpen_id(String open_id) {
        this.open_id = open_id;
    }

    public Integer getIntegral() {
        return integral;
    }

    public void setIntegral(Integer integral) {
        this.integral = integral;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Boolean getUnbind() {
        return unbind;
    }

    public void setUnbind(Boolean unbind) {
        this.unbind = unbind;
    }

    public String getShopCode() {
        return shopCode;
    }

    public void setShopCode(String shopCode) {
        this.shopCode = shopCode;
    }

    public Long getPointCent() {
        return pointCent;
    }

    public void setPointCent(Long pointCent) {
        this.pointCent = pointCent;
    }

    public String getUnion_id() {
        return union_id;
    }

    public void setUnion_id(String union_id) {
        this.union_id = union_id;
    }
}
