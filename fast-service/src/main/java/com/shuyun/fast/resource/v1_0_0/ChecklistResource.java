package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.router.ApiHandlerRouter;
import com.shuyun.fast.v1_0_0.domain.MemberId;
import com.shuyun.fast.v1_0_0.param.checklist.ChecklistAddParam;
import com.shuyun.fast.v1_0_0.param.checklist.ChecklistDeleteParam;
import com.shuyun.fast.v1_0_0.param.checklist.ChecklistGetParam;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.validation.Valid;

@Tag(name = "黑名单场景")
@ExecuteOn("blocking")
@Controller("/v1/0.0/api/checklist")
@Introspected
public class ChecklistResource {

    private final ApiHandlerRouter handlerRouter;

    public ChecklistResource(ApiHandlerRouter handlerRouter){
        this.handlerRouter = handlerRouter;
    }

    @Operation(summary = "黑名单查询")
    @Post("/get")
    @Api(name = ApiTags.API_NAME_CHECKLIST_GET)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<MemberId> get(@Valid @Body ChecklistGetParam param){
        MemberId result = (MemberId)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "黑名单添加")
    @Post("/add")
    @Api(name = ApiTags.API_NAME_CHECKLIST_ADD)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> add(@Valid @Body ChecklistAddParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }
    @Operation(summary = "黑名单删除")
    @Post("/delete")
    @Api(name = ApiTags.API_NAME_CHECKLIST_DELETE)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> delete(@Valid @Body ChecklistDeleteParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

}
