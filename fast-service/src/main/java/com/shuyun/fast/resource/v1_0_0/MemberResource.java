package com.shuyun.fast.resource.v1_0_0;


import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.router.ApiHandlerRouter;
import com.shuyun.fast.v1_0_0.domain.DynamicCode;
import com.shuyun.fast.v1_0_0.domain.MemberId;
import com.shuyun.fast.v1_0_0.param.member.*;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.v1_0_0.result.MemberBindResult;
import com.shuyun.fast.v1_0_0.result.MemberGetResult;
import com.shuyun.fast.v1_0_0.result.MemberRegisterResult;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;

@Tag(name = "会员场景")
@ExecuteOn("blocking")
@Controller("/v1/0.0/api/member")
@Introspected
public class MemberResource {

    private final ApiHandlerRouter handlerRouter;

    public MemberResource(ApiHandlerRouter handlerRouter){
        this.handlerRouter = handlerRouter;
    }

    @Operation(summary = "会员查询")
    @Post("/get")
    @Api(name = ApiTags.API_NAME_MEMBER_GET)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<MemberGetResult> get(@Valid @Body MemberGetParam param){
        MemberGetResult result = (MemberGetResult)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "会员注册")
    @Post("/register")
    @Api(name = ApiTags.API_NAME_MEMBER_REGISTER)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<MemberRegisterResult> register(@Valid @Body MemberRegisterParam param){
        MemberRegisterResult result = (MemberRegisterResult)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "会员绑定")
    @Post("/bind")
    @Api(name = ApiTags.API_NAME_MEMBER_REGISTER)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<MemberBindResult> bind(@Valid @Body MemberBindParam param){
        MemberBindResult result = (MemberBindResult)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "会员解绑")
    @Post("/unbind")
    @Api(name = ApiTags.API_NAME_MEMBER_UNBIND)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<MemberId> unbind(@Valid @Body MemberUnbindParam param){
        MemberId result = (MemberId)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "会员修改")
    @Post("/modify")
    @Api(name = ApiTags.API_NAME_MEMBER_MODIFY)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<MemberId> modify(@Valid @Body MemberModifyParam param){
        MemberId result = (MemberId)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "手机号修改")
    @Post("/mobile/modify")
    @Api(name = ApiTags.API_NAME_MEMBER_MODIFY)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<MemberId> mobileModify(@Valid @Body MemberMobileModifyParam param){
        MemberId result = (MemberId)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "会员注销")
    @Post("/deregister")
    @Api(name = ApiTags.API_NAME_MEMBER_DEREGISTER)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<MemberId> deregister(@Valid @Body MemberDeregisterParam param){
        MemberId result = (MemberId)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "动态码获取")
    @Post("/dynamicCode/get")
    @Api(name = ApiTags.API_NAME_MEMBER_DYNAMICCODE_GET)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<DynamicCode> dynamicCodeGet(@Valid @Body MemberDynamicCodeGetParam param){
        DynamicCode result = (DynamicCode)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "动态码识别")
    @Post("/dynamicCode/identify")
    @Api(name = ApiTags.API_NAME_MEMBER_DYNAMICCODE_IDENTIFY)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<MemberId> dynamicCodeIdentify(@Valid @Body MemberDynamicCodeIdentifyParam param){
        MemberId result = (MemberId)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }
}
