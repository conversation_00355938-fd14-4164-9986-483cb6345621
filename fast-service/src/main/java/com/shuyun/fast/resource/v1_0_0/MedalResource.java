package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.router.ApiHandlerRouter;
import com.shuyun.fast.v1_0_0.domain.Medal;
import com.shuyun.fast.v1_0_0.param.medal.MedalGetParam;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "勋章场景")
@ExecuteOn("blocking")
@Controller("/v1/0.0/api/medal")
@Introspected
public class MedalResource {


    private final ApiHandlerRouter handlerRouter;

    public MedalResource(ApiHandlerRouter handlerRouter){
        this.handlerRouter = handlerRouter;
    }

    @Operation(summary = "会员勋章查询")
    @Post("/get")
    @Api(name = ApiTags.API_NAME_MEDAL_GET)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<List<Medal>> get(@Valid @Body MedalGetParam param){
        List<Medal> result = (List<Medal>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

}
