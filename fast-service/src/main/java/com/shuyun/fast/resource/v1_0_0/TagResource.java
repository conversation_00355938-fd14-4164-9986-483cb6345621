package com.shuyun.fast.resource.v1_0_0;

import com.shuyun.cdp.tags.vo.category.OpenCategoryVo;
import com.shuyun.cdp.tags.vo.openapi.OpenTagContent;
import com.shuyun.cdp.tags.vo.openapi.OpenTagVo;
import com.shuyun.fast.annotation.Api;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.router.ApiHandlerRouter;
import com.shuyun.fast.v1_0_0.param.tag.*;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "标签场景")
@ExecuteOn("blocking")
@Controller("/v1/0.0/api/tag")
@Introspected
public class TagResource {

    private final ApiHandlerRouter handlerRouter;

    public TagResource(ApiHandlerRouter handlerRouter){
        this.handlerRouter = handlerRouter;
    }

    @Operation(summary = "标签目录列表")
    @Post("/category/list")
    @Api(name = ApiTags.API_NAME_TAG_CATEGORY_LIST)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<List<OpenCategoryVo>> categoryList(@Valid @Body TagCategoryListParam param){
        List<OpenCategoryVo> result = (List<OpenCategoryVo>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "标签列表")
    @Post("/list")
    @Api(name = ApiTags.API_NAME_TAG_LIST)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<PageResult<OpenTagVo>> list(@Valid @Body TagListParam param){
        PageResult<OpenTagVo> result = (PageResult<OpenTagVo>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "打标")
    @Post("/tagging")
    @Api(name = ApiTags.API_NAME_TAG_TAGGING)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> tagging(@Valid @Body TagTaggingParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "去标")
    @Post("/untagging")
    @Api(name = ApiTags.API_NAME_TAG_UNTAGGING)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<Void> untagging(@Valid @Body TagUntaggingParam param){
        Void result = (Void)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(hidden = true, summary = "标签下用户列表")
    @Post("/user/list")
    @Api(name = ApiTags.API_NAME_TAG_USER_LIST)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<PageResult<String>> userList(@Valid @Body TagUserListParam param){
        PageResult<String> result = (PageResult<String>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "用户标签列表")
    @Post("/user/tags")
    @Api(name = ApiTags.API_NAME_TAG_USER_TAGS)
    @Consumes(MediaType.APPLICATION_JSON)
    public ApiResult<List<OpenTagContent>> userTags(@Valid @Body TagUserTagsParam param){
        List<OpenTagContent> result = (List<OpenTagContent>)handlerRouter.route(param).handle(param);
        return ApiResult.success(result);
    }

}
