<configuration>
    <contextName>fast-service</contextName>
    <property name="SERVICE_NAME" value="${SERVICE_NAME:-fast-service}"/>

    <property name="LOG_PATTERN" value="[%d{yyyy-MM-dd HH:mm:ss.SSS,GMT+8}] [%-5level] [%class{0}:%method:%line] [%t] [-[%msg]-] %n"/>
    <property name="system.logging.format" value="${system.logging.format:-${LOG_PATTERN}}"/>
    <property name="system.logging.dir" value="/var/log"/>
    <property name="system.logging.bakdir" value="/var/logbak"/>
    <property name="system.logging.async.queueSize" value="${system.logging.async.queueSize:-10240}" />
    <property name="system.logging.async.neverBlock" value="${system.logging.async.neverBlock:-true}" />
    <property name="system.logging.async.includeCallerData" value="${system.logging.async.includeCallerData:-true}" />
    <property name="APP_VERSION" value="${APP_VERSION:-0.0.1}"/>
    <property name="ENVIRONMENT" value="${ENVIRONMENT:-dev}"/>
    <property name="SANDBOX" value="${SANDBOX:-base}"/>
    <property name="MESOS_TASK_ID" value="${MESOS_TASK_ID:-001}"/>


    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned the type
             ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
        <encoder>
            <pattern>%cyan(%d{HH:mm:ss.SSS}) %gray([%thread]) %highlight(%-5level) %magenta(%logger{36}) - %msg%n
            </pattern>
        </encoder>
    </appender>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${system.logging.format}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>
            ${system.logging.dir}/${SERVICE_NAME}/${APP_VERSION}/${ENVIRONMENT}/${SANDBOX}/${MESOS_TASK_ID}/${SERVICE_NAME}.log
        </file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>
                ${system.logging.bakdir}/${SERVICE_NAME}/${APP_VERSION}/${ENVIRONMENT}/${SANDBOX}/${MESOS_TASK_ID}/${SERVICE_NAME}.log.%d{yyyyMMdd}.%i
            </fileNamePattern>
            <!--单个日志文件最大值-->
            <maxFileSize>500MB</maxFileSize>
            <!--日志保留天数-->
            <maxHistory>7</maxHistory>
            <!--日志文件总体的最大值-->
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${LOG_PATTERN}</pattern>
        </layout>
    </appender>

    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>${system.logging.async.queueSize}</queueSize>
        <neverBlock>${system.logging.async.neverBlock}</neverBlock>
        <discardingThreshold>20</discardingThreshold>
        <includeCallerData>${system.logging.async.includeCallerData}</includeCallerData>
        <appender-ref ref="FILE"/>
    </appender>

    <appender name="ASYNC_CONSOLE" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>${system.logging.async.queueSize}</queueSize>
        <neverBlock>${system.logging.async.neverBlock}</neverBlock>
        <discardingThreshold>20</discardingThreshold>
        <includeCallerData>${system.logging.async.includeCallerData}</includeCallerData>
        <appender-ref ref="CONSOLE"/>
    </appender>


    <root level="info">
        <appender-ref ref="ASYNC_CONSOLE"/>
        <appender-ref ref="ASYNC_FILE"/>
    </root>
    <Logger name="org.apache.kafka.clients.producer.ProducerConfig" level="ERROR"/>
    <Logger name="org.apache.kafka.clients.consumer.ConsumerConfig" level="ERROR"/>
    <Logger name="org.apache.kafka.clients.admin.AdminClientConfig" level="ERROR"/>
<!--    <logger name="io.micronaut.context" level="DEBUG"/>-->
</configuration>
