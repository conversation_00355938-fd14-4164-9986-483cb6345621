datasources:
  default:
    dialect: MYSQL
    connectionInitSql: set names utf8mb4;
    jdbcUrl: jdbc:mysql://${database.host}:${database.port}/${database.schema.name}?useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true&serverTimezone=Asia/Shanghai
    username: ${database.username}
    password: ${database.password}
    driverClassName: com.mysql.cj.jdbc.Driver
    maximum-pool-size: 50
    minIdle: 15
    connectionTimeout: 60000
    keepaliveTime: 60000
    allow-connection-per-operation: true
    transaction-per-operation: false

flyway:
  datasources:
    default:
      enabled: true
      locations:
        - classpath:/db/migration

#swagger-ui:
#  enabled: true
kafka:
  bootstrap:
    servers: ${kafka.address}

fast:
  redis:
    enable: true
    ssl: false
    address: redis://${redis.address}
    password: ${redis.password}
    poolSize: 64
    database: 0
    nettyThread: 64

passport:
  enable: true
  clientId: fast-service
  clientName: fast-service

member:
  ebrand:
    #    enable: {fast.member.ebrand.enable: false}
    enable: false

benefit:
  enable: false
#  enable: {fast.benefit.ebrand.enable}
  consumer:
    group: fast-service
  selector:
    refresh:
      rate: 2h
      delay: 20m

snowflake:
  enable: true