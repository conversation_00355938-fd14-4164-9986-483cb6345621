CREATE TABLE IF NOT EXISTS `t_biz_cache` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `tenantId` VARCHAR(64) COMMENT '租户id' COLLATE utf8mb4_general_ci DEFAULT NULL,
    `bizCode` VARCHAR(64) COMMENT '业务编码' COLLATE utf8mb4_general_ci NOT NULL,
    `cacheType` VARCHAR(64) COMMENT '缓存类型' COLLATE utf8mb4_general_ci NOT NULL,
    `value` text COMMENT '缓存值' DEFAULT NULL,
    `createTime` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updateTime` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY ( `id` ),
    UNIQUE KEY `idx` ( `tenantId`, `bizCode`, `cacheType` )
) ENGINE = INNODB AUTO_INCREMENT=0 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;