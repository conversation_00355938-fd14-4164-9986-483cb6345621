kind: Service
specVersion: v4
metadata:
  name: fast-service
  apiVersion: v1
  accessPoint:
    container: fast/fast-service
  middleware:
    mysql: {}

containers:
  - name: fast/fast-service
    imagePullPolicy: PullIfNotPresent
    ports:
      - name: fast-service
        containerPort: 8080
        targetPort: 0
        protocol: tcp
profiles:
  - mem: 4096
    cpu: 0.5
    name: default
    replicas: 1
    envs:
      profileActive: dev
    containers:
      - mem: 4096
        cpu: 0.5
        name: fast/fast-service