FROM hub.shuyun.com/base/java:openjdk-11.0.20

WORKDIR /fast-service
COPY target/fast-service.jar /fast-service/fast-service.jar
COPY src/main/resources/application.properties /fast-service/application.properties
COPY src/main/resources/application-dev.yml /fast-service/application-dev.yml
COPY src/main/resources/application-spectrum.yml /fast-service/application-spectrum.yml

ENV spectrum.disableAutoUploadProperties=true

EXPOSE 8080

CMD ["java", "-Dmicronaut.environments=spectrum", "-XX:HeapDumpPath=/var/log/fast-service/heap.hprof", "-XX:ParallelGCThreads=4", "-XX:-CICompilerCountPerCPU", "-XX:CICompilerCount=4", "-XX:NewRatio=1", "-Xms1228m",  "-jar", "fast-service.jar"]
